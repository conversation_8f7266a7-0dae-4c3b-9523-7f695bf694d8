package cz.equa.camapp.utils;

import cz.equa.camapp.lovs.LovApplStat;
import io.micrometer.common.util.StringUtils;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DealerNotificationsUtils {

    private static final String PATTERN_RML = "^(RML_).*";
    private static final String PATTERN_BSL = "^(BSL_).*";
    private static final String PATTERN_RCL_STD_RCL_REFI_RCC_ROD = "^(RCL_STANDARD|RCL_REFI|RCC_|ROD_).*";
    private static final String PATTERN_RCL_RCC_ROD = "^(RCL_|RCC_|ROD_).*";

    public enum Role {
        EFA, BANKER, CLIENT
    }

    private static final Map<String, Role> CHANNEL_TO_ROLE = new HashMap<>();

    static {
        CHANNEL_TO_ROLE.put("10", Role.BANKER);
        CHANNEL_TO_ROLE.put("14", Role.EFA);
        CHANNEL_TO_ROLE.put("30", Role.EFA);
        CHANNEL_TO_ROLE.put("32", Role.EFA);
        CHANNEL_TO_ROLE.put("15", Role.CLIENT);
        CHANNEL_TO_ROLE.put("18", Role.CLIENT);
        CHANNEL_TO_ROLE.put("35", Role.CLIENT);
    }

    private static final Map<String, List<DealerNotificationRule>> RULES = new HashMap<>();

    static {
        //GNV
        RULES.put(LovApplStat.GUAR_OFFER_CREATED.getCode(), List.of(new DealerNotificationRule(PATTERN_RML, DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.BANKER))));
        //PRDN
        RULES.put(LovApplStat.PRODUCT_EXISTS.getCode(), List.of(new DealerNotificationRule("^(RCL_STANDARD|RCL_REFI)", DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA))));
        //FNLEND
        RULES.put(LovApplStat.FINALIZING_COMPLETED.getCode(), List.of(new DealerNotificationRule("^(RCL_REFI)", DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA))));
        //NBDV
        RULES.put(LovApplStat.OFFER_CREATED.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RCL_STD_RCL_REFI_RCC_ROD, DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA)),
                        new DealerNotificationRule(PATTERN_RCL_STD_RCL_REFI_RCC_ROD, DealerNotificationsUtils.Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_BSL, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_BSL, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))

                )
        );
        //NBDU
        RULES.put(LovApplStat.OFFER_UPDATE_WITH_ADVISOR.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RCL_RCC_ROD, DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA)),
                        new DealerNotificationRule(PATTERN_RCL_RCC_ROD, DealerNotificationsUtils.Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
        //ZDPSCH
        RULES.put(LovApplStat.CONDITIONALLY_APPROVED.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RCL_RCC_ROD, DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA)),
                        new DealerNotificationRule(PATTERN_RCL_RCC_ROD, DealerNotificationsUtils.Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
        //FNLSCR
        RULES.put(LovApplStat.APPL_IN_THE_FINAL_SCORING.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
        //SN - stejné jako FNLSCR
        RULES.put(LovApplStat.SN.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
        //ZDZAM
        RULES.put(LovApplStat.APPL_REJECTED.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RCL_STD_RCL_REFI_RCC_ROD, DealerNotificationsUtils.Role.EFA, List.of(DealerNotificationsUtils.Role.EFA)),
                        new DealerNotificationRule(PATTERN_RCL_STD_RCL_REFI_RCC_ROD, DealerNotificationsUtils.Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
        //STR
        RULES.put(LovApplStat.CANCEL.getCode(), List.of(
                        new DealerNotificationRule(PATTERN_RCL_STD_RCL_REFI_RCC_ROD, DealerNotificationsUtils.Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.CLIENT, List.of(DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.EFA, List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER)),
                        new DealerNotificationRule(PATTERN_RML, Role.BANKER, List.of(DealerNotificationsUtils.Role.BANKER))
                )
        );
    }

    @Nullable
    public static Role determineRoleByChannel(String ftp) {
        if (StringUtils.isBlank(ftp) || ftp.length() < 2) {
            return null;
        }
        return CHANNEL_TO_ROLE.get(ftp.substring(0, 2));
    }

    public static boolean isForEmailNotification(String state, String productId, Role ftpRole) {
        if (StringUtils.isBlank(productId) || StringUtils.isBlank(state) || ftpRole == null) {
            return false;
        }
        List<DealerNotificationRule> rules = RULES.get(state);
        return rules.stream().anyMatch(rule -> rule.matches(productId, ftpRole));
    }

    public static List<Role> getEmailNotificationRoles(String state, String productId, Role ftpRole) {
        List<Role> result = List.of();
        if (StringUtils.isBlank(productId) || StringUtils.isBlank(state)) {
            return result;
        }
        List<DealerNotificationRule> rules = RULES.get(state);
        if (rules != null) {
            DealerNotificationRule rule = rules.stream().filter(r -> r.matches(productId, ftpRole)).findFirst().orElse(null);
            if (rule != null) {
                return rule.getRoleToNotify();
            }
        }
        return result;
    }
}
