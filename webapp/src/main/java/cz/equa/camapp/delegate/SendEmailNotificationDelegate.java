package cz.equa.camapp.delegate;

import cz.equa.camapp.lovs.LovApplVariantTp;
import cz.equa.camapp.lovs.LovBusProdTp;
import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.model.DealerInfoDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.GetLoanApplicationResult;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.HadPartnersService;
import cz.equa.camapp.service.FreeMarkerTemplateService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.utils.DealerNotificationsUtils;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.impl.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cz.equa.camapp.utils.ApplicationUtils.getAnonymizedFullName;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class SendEmailNotificationDelegate extends ProcessJavaDelegate {

    protected static final String APPL_ID = "applId";
    protected static final String FULL_NAME = "fullName";
    protected static final String PARTY_ID = "partyId";
    protected static final String APPROVAL_DATE = "approvalDate";
    protected static final String MONEY_TRANSFER_DATE = "moneyTransferDate";
    protected static final String REJECT_DATE = "rejectDate";
    protected static final String CANCELLATION_DATE = "cancellationDate";
    protected static final String REPRE_DRAW_DATE = "repreDrawDate";
    protected static final String PRODUCT = "product";
    protected static final String MATURITY = "maturity";
    protected static final String INST_AMT = "instAmt";
    protected static final String APPL_TP_ID = "applTpId";
    protected static final String AMOUNT = "amount";
    protected static final String OBLGTN_AMOUNT = "oblgtnAmount";
    protected static final String PROVIDER = "provider";
    protected static final String INTRS_RX = "intrsRx";
    protected static final String FTP_ROLE = "ftpRole";
    protected static final String RECIPIENT_ROLE = "recipientRole";
    protected static final String CREATOR_NAME = "creatorName";
    protected static final String CREATOR_COMPANY = "creatorCompany";
    protected static final String ACTUAL_DATE = "actualDate";

    LoanApplicationDTO application = null;
    NewPersonDTO applicationOwner = null;
    List<NewApplVariantDTO> applVariantDTOList = null;
    GetMortApplDTO mtgApplication = null;
    List<MortApplVariantDTO> mtgVariants = null;
    CreditCardApplDTO creditCardApplication = null;
    List<ObligationDTO> obligations = null;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private FreeMarkerTemplateService templateService;

    @Autowired
    private HadPartnersService hadService;

    @Autowired
    private JavaMailSender javaMailSender;

    @Value("${environment.id:PROD}")
    private String environmentId;

    @Value("${smtp_from}")
    private String smtpFrom;

    @Override
    protected void saveCurrentApplicationState() throws ServiceException {
        // DO NOTHING
    }

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) {
        log.info("start SendEmailNotificationDelegate");
        String state = getStringProcessVariable(ProcessVariable.APPLICATION_STATE);
        String processTp = getStringProcessVariable(ProcessVariable.PROCESS_TP);
        String productId = getStringProcessVariable(ProcessVariable.PRODUCT_ID);
        // application dealer FTP
        String ftp = getStringProcessVariable(ProcessVariable.FTP);
        DealerInfoDTO dealerInfo = hadService.getDealerDetailWithCache(ftp, getCorrelationId());
        // receiver of notification
        String recipientId = getStringProcessVariable(ProcessVariable.RECIPIENT_ID);
        DealerInfoDTO recipient = hadService.getDealerDetailWithCache(recipientId, getCorrelationId());
        // email for sending notification
        String email = recipient.getEmail();
        // for EFA is client name limited
        String recipientRole = DealerNotificationsUtils.determineRoleByChannel(recipientId) != null ? Objects.requireNonNull(DealerNotificationsUtils.determineRoleByChannel(recipientId)).toString() : "";

        String template = getTemplateName(state, productId, email);
        if (StringUtils.isNotBlank(template) && StringUtils.isNotBlank(recipientRole)) {
            try {
                getApplication();
                var data = mapData(processTp, productId, recipientRole, dealerInfo);
                sendEmail(email, renderSubject(template, data), renderBody(template, data));
            } catch (Exception e) {
                log.error("Email notification was not sent. Error: {}", e.getMessage());
            }
        }
    }

    protected String getTemplateName(String state, String productId, String email) {
        if (StringUtils.isBlank(state) || StringUtils.isBlank(email) || StringUtils.isBlank(productId)) {
            log.info("template null <- state: {}, productId: {}, email: {}", state, productId, email);
            return null;
        }

        String commonProductPattern = "^(RCL_STANDARD|RCL_REFI|RCC_.*|ROD_.*|RML_.*|BSL_.*)";
        String rmlProductPattern = "^(RML_.*)";
        String prdnProductPattern = "^(RCL_STANDARD|RCL_REFI)";
        String fnlendProductPattern = "^RCL_REFI";

        Map<String, String> stateTemplateMap = new HashMap<>();
        stateTemplateMap.put("ZDPSCH", "NBDU");
        stateTemplateMap.put("SN", "FNLSCR");

        List<Map.Entry<String, String>> stateProductPatterns = List.of(
                Map.entry("GNV", rmlProductPattern),
                Map.entry("FNLSCR", rmlProductPattern),
                Map.entry("NBDV", commonProductPattern),
                Map.entry("NBDU", commonProductPattern),
                Map.entry("ZDPSCH", commonProductPattern),
                Map.entry("ZDZAM", commonProductPattern),
                Map.entry("STR", commonProductPattern),
                Map.entry("PRDN", prdnProductPattern),
                Map.entry("FNLEND", fnlendProductPattern),
                Map.entry("SN", rmlProductPattern)
        );

        for (Map.Entry<String, String> entry : stateProductPatterns) {
            String patternState = entry.getKey();
            String productPattern = entry.getValue();

            if (state.equals(patternState) && productId.toUpperCase().matches(productPattern)) {
                String template = stateTemplateMap.getOrDefault(state, state);
                if (productId.toUpperCase().startsWith("RML")) {
                    template = "RML-" + template;
                }
                if (productId.toUpperCase().startsWith("BSL")) {
                    template = "BSL-" + template;
                }
                log.info("template {}", template);
                return template;
            }
        }

        log.info("template null");
        return null;
    }

    protected Map<String, Object> mapData(String processTp, String productId, String recipientRole, DealerInfoDTO dealerInfo) {
        Map<String, Object> model = new HashMap<>();
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));

        String busApplId = getStringProcessVariable(ProcessVariable.BUS_APPL_ID);
        model.put(APPL_ID, busApplId);
        model.put(RECIPIENT_ROLE, recipientRole);
        if (dealerInfo != null) {
            model.put(CREATOR_NAME, dealerInfo.getFirstName() + " " + dealerInfo.getLastName());
            model.put(CREATOR_COMPANY, dealerInfo.getCompanyName());
        }
        if (applicationOwner != null) {
            model.put(FULL_NAME, getAnonymizedFullName(applicationOwner.getFirstName(), applicationOwner.getFamilyName(), "EFA".equals(recipientRole)));
            model.put(PARTY_ID, applicationOwner.getSiebelId());
        }

        setProductInfo(model, productId, processTp);

        if ((productId != null && productId.startsWith("RML_")) || LovProcessTp.ADD_MORTGAGE.getCode().equals(processTp)) {
            model.put(REPRE_DRAW_DATE, today);
            model.put(ACTUAL_DATE, today);
            model.put(APPROVAL_DATE, today);
            model.put(REJECT_DATE, today);
            model.put(CANCELLATION_DATE, today);
            DealerNotificationsUtils.Role ftpRole = DealerNotificationsUtils.determineRoleByChannel(mtgApplication.getFirstTouchPoint());
            if (ftpRole != null) {
                model.put(FTP_ROLE, ftpRole.toString());
            }
            if (mtgVariants != null) {
                MortApplVariantDTO variant = findSuitableVariantMort(mtgVariants);
                if (variant != null) {
                    model.put(AMOUNT, variant.getFinaAmt());
                    model.put(MATURITY, variant.getInstlCnt());
                    model.put(INST_AMT, variant.getInstlAmt());
                }
            }
        } else if (processTp != null && processTp.contains("CREDIT_CARD")) {
            model.put(REPRE_DRAW_DATE, today);
            model.put(REJECT_DATE, today);
            model.put(CANCELLATION_DATE, today);
            if (creditCardApplication != null && applVariantDTOList != null) {
                model.put(APPROVAL_DATE, creditCardApplication.getValidFromDate());
                NewApplVariantDTO variant = findSuitableVariant(applVariantDTOList);
                if (variant != null) {
                    model.put(AMOUNT, variant.getFinaAmt());
                    model.put(MATURITY, variant.getInstlCnt());
                    model.put(INST_AMT, variant.getInstlAmt());
                    model.put(INTRS_RX, variant.getIntrsRx().multiply(BigDecimal.valueOf(100)));
                }
            }
        } else {
            model.put(REPRE_DRAW_DATE, today);
            model.put(REJECT_DATE, today);
            model.put(CANCELLATION_DATE, today);
            if (application != null && applVariantDTOList != null) {
                model.put(APPROVAL_DATE, application.getValidFromDate());
                model.put(MONEY_TRANSFER_DATE, application.getMoneyTransferDate());
                NewApplVariantDTO variant = findSuitableVariant(applVariantDTOList);
                if (variant != null) {
                    model.put(AMOUNT, variant.getFinaAmt());
                    model.put(MATURITY, variant.getInstlCnt());
                    model.put(INST_AMT, variant.getInstlAmt());
                    model.put(INTRS_RX, variant.getIntrsRx().multiply(BigDecimal.valueOf(100)));
                }

                if (!CollectionUtil.isEmpty(obligations)) {
                    Optional<ObligationDTO> obligation = obligations.stream().filter(o -> Boolean.TRUE.equals(o.getPrimaryOblgtn())).findFirst();

                    obligation.ifPresent(o -> {
                        BigInteger loanAmount = Optional.ofNullable(o.getScTotLoanAmt())
                                .map(BigDecimal::toBigInteger)
                                .orElse(BigInteger.ZERO);
                        model.put(OBLGTN_AMOUNT, loanAmount);

                        String provider = Optional.ofNullable(o.getScFinInstnCode())
                                .orElse(o.getClFinInstnId());
                        model.put(PROVIDER, provider);
                    });
                }

            }
        }
        log.info("data {}", model);
        return model;
    }

    protected void setProductInfo(Map<String, Object> model, String productId, String processTp) {
        if (mtgApplication != null && ((productId != null && productId.startsWith("RML_")) || LovProcessTp.ADD_MORTGAGE.getCode().equals(processTp))) {
            model.put(PRODUCT, mtgApplication.getBusProdSubTp());
            model.put(APPL_TP_ID, mtgApplication.getApplTpId());
        } else if (processTp != null && processTp.contains("CREDIT_CARD") && creditCardApplication != null) {
            model.put(PRODUCT, creditCardApplication.getBusProdSubTp());
            model.put(APPL_TP_ID, creditCardApplication.getApplTpId());
        } else {
            model.put(PRODUCT, application.getBusProdSubTp());
            model.put(APPL_TP_ID, application.getApplTpId());
        }
    }

    protected NewApplVariantDTO findSuitableVariant(List<NewApplVariantDTO> variants) {
        Map<String, Integer> preferredVariants = getOrderedVariants();

        return variants.stream()
                .filter(variant -> preferredVariants.containsKey(variant.getApplVariantTpId()))
                .sorted(Comparator.comparingInt(variant -> preferredVariants.get(variant.getApplVariantTpId())))
                .findFirst()
                .orElse(null);
    }

    protected MortApplVariantDTO findSuitableVariantMort(List<MortApplVariantDTO> mtgVariants) {
        Map<String, Integer> preferredVariants = getOrderedVariants();

        return mtgVariants.stream()
                .filter(variant -> preferredVariants.containsKey(variant.getApplVariantTpId()))
                .sorted(Comparator.comparingInt(variant -> preferredVariants.get(variant.getApplVariantTpId())))
                .findFirst()
                .orElse(null);
    }

    protected Map<String, Integer> getOrderedVariants() {
        Map<String, Integer> result = new HashMap<>();
        result.put(LovApplVariantTp.APR.getCode(), 0);
        result.put(LovApplVariantTp.SEL.getCode(), 1);
        result.put(LovApplVariantTp.OFR.getCode(), 2);
        result.put(LovApplVariantTp.GAR.getCode(), 3);
        result.put(LovApplVariantTp.REQ.getCode(), 4);
        return result;
    }


    protected void getApplication() throws ServiceException {
        String processTp = getStringProcessVariable(ProcessVariable.PROCESS_TP);
        String productId = getStringProcessVariable(ProcessVariable.PRODUCT_ID);

        List<String> variants = Arrays.asList(LovApplVariantTp.REQ.getCode(), LovApplVariantTp.SEL.getCode(), LovApplVariantTp.OFR.getCode(), LovApplVariantTp.APR.getCode(), LovApplVariantTp.GAR.getCode());
        List<LovApplVariantTp> variantTps = Arrays.asList(LovApplVariantTp.REQ, LovApplVariantTp.SEL, LovApplVariantTp.OFR, LovApplVariantTp.APR, LovApplVariantTp.GAR);

        if (productId != null && productId.toUpperCase().matches("^(RML_.*)")) {
            GetMortgageDTO getMortgageDTO = new GetMortgageDTO();
            getMortgageDTO.setApplFlag(true);
            getMortgageDTO.setPersonsFlag(true);
            getMortgageDTO.setIncomeFlag(false);
            getMortgageDTO.setApplVariantsFlag(true);
            getMortgageDTO.setApplVariantTpId(variants);
            GetMortApplResponseDTO applResp = applicationService.getMortgageAppl(getApplKey(), getBusApplId(), getMortgageDTO, getCorrelationId());

            String busApplId = StringUtils.isNotBlank(getBusSubApplId()) ? getBusSubApplId() : getBusApplId();
            mtgApplication = applResp.getMortgageAppls().stream()
                    .filter(appl -> appl.getApplId().getBusApplId().equals(busApplId))
                    .findFirst().orElseThrow();
            applicationOwner = ApplicationMapper.INSTANCE.esbToDto(applResp.getMainApplicant(busApplId));
            mtgVariants = mtgApplication.getApplVariants();
        } else if ((processTp != null && Arrays.asList(LovProcessTp.ADD_CREDIT_CARD.getCode(), LovProcessTp.CH_CREDIT_CARD_INCR.getCode()).contains(processTp) || (productId != null && productId.startsWith(LovBusProdTp.RETAIL_CREDIT_CARD.getCode())))) {
            GetCreditCardApplResponseDTO creditCard = applicationService.getCreditCard(getApplKey(),
                    getBusApplId(),
                    true,
                    false,
                    variants,
                    false,
                    getCorrelationId());
            application = ApplicationMapper.INSTANCE.creditCardApplToLoanAppl(creditCard.getCreditCardAppl());
            applicationOwner = creditCard.getPrimaryOwner();
            applVariantDTOList = creditCard.getApplVariants();
            creditCardApplication = creditCard.getCreditCardAppl();
        } else if ((productId != null && productId.startsWith(LovBusProdTp.RETAIL_OVERDRAFT.getCode())) || (processTp != null && processTp.equals(LovProcessTp.ADD_ROD.getCode()))) {
            GetOverdraftApplResponseDTO overdraft = applicationService.getOverdraft(getApplKey(),
                    getBusApplId(),
                    true,
                    false,
                    variants,
                    getCorrelationId());
            application = ApplicationMapper.INSTANCE.overdraftApplToLoanAppl(overdraft.getOverdraftAppl());
            applicationOwner = overdraft.getPrimaryOwner();
            applVariantDTOList = overdraft.getApplVariants();
        } else {
            GetLoanApplicationResult loanApplResult = applicationService.getApplicationWithProperties(getApplKey(),
                    true,
                    false,
                    variantTps,
                    true,
                    getCorrelationId());
            application = loanApplResult.getLoanApplication();
            applicationOwner = loanApplResult.getPrimaryOwner();
            if (loanApplResult.getApplVariants() != null) {
                applVariantDTOList = loanApplResult.getApplVariants().getAllVariants();
            }

            if (loanApplResult.getObligations() != null) {
                obligations = loanApplResult.getObligations();
            }
        }
    }

    protected String renderSubject(String template, Map<String, Object> model) throws TemplateException, IOException {
        return templateService.renderTemplate(template.toUpperCase() + "-subject.ftl", model);
    }

    protected String renderBody(String template, Map<String, Object> model) throws TemplateException, IOException {
        return templateService.renderTemplate(template.toUpperCase() + "-body.ftl", model);
    }

    protected void sendEmail(String to, String subject, String body) {
        if (isTesting()) {
            log.info("Sending email to: {} subject: {} body: {}", to, subject, body);
        }
        var message = javaMailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setTo(to);
            helper.setFrom(smtpFrom);
            helper.setSubject(subject);
            helper.setText(body, true);
            javaMailSender.send(message);
            log.info("Sent email to: {} subject: {}", to, subject);
        } catch (MailException | MessagingException e) {
            log.error("Failed send email to: {} subject: {} with error: {}", to, subject, e.getMessage());
        }
    }

    protected boolean isTesting() {
        return org.apache.commons.lang3.StringUtils.isNotBlank(environmentId) && (
                environmentId.equalsIgnoreCase("presit")
                        || environmentId.equalsIgnoreCase("eaas")
                        || environmentId.equalsIgnoreCase("tfx1")
                        || environmentId.equalsIgnoreCase("tfix1")
                        || environmentId.equalsIgnoreCase("prep")
                        || environmentId.equalsIgnoreCase("preprod")
        );
    }
}
