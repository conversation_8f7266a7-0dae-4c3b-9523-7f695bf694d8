<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1btzhpt" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.21.0">
  <bpmn:process id="RekoApproval" name="REKO Approval" isExecutable="true" camunda:historyTimeToLive="7" camunda:isStartableInTasklist="false">
    <bpmn:subProcess id="Activity_0ccyx7x">
      <bpmn:incoming>Flow_1c1up2e</bpmn:incoming>
      <bpmn:startEvent id="Event_Start">
        <bpmn:outgoing>Flow_1l2k4ul</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:subProcess id="Sub_mainProcess">
        <bpmn:incoming>Flow_1l2k4ul</bpmn:incoming>
        <bpmn:outgoing>Flow_0jbybxh</bpmn:outgoing>
        <bpmn:subProcess id="Sub_Finalization" name="Finalization" triggeredByEvent="true">
          <bpmn:task id="Activity_SetApprovalParameters_2" name="Set Approval Parameters">
            <bpmn:documentation>Set processPhase and processPart</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:outputParameter name="processPhase">3</camunda:outputParameter>
                <camunda:outputParameter name="processPart">KO_PRE_CONTRACT_GEN</camunda:outputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0e5uw69</bpmn:incoming>
            <bpmn:outgoing>Flow_1slf1ue</bpmn:outgoing>
          </bpmn:task>
          <bpmn:serviceTask id="Activity_stateFNLSCR" name="stateFNLSCR" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">FNLSCR</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0eealr6</bpmn:incoming>
            <bpmn:outgoing>Flow_0e5uw69</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0e5uw69" sourceRef="Activity_stateFNLSCR" targetRef="Activity_SetApprovalParameters_2" />
          <bpmn:serviceTask id="Activity_stateNBDV" name="stateNBDV" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">NBDV</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1ijninc</bpmn:incoming>
            <bpmn:outgoing>Flow_0xriihl</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0xriihl" sourceRef="Activity_stateNBDV" targetRef="Activity_TriggerEmailNotification" />
          <bpmn:sequenceFlow id="Flow_0eealr6" sourceRef="Sub_applicationFinalized" targetRef="Activity_stateFNLSCR" />
          <bpmn:startEvent id="Event_FINA" name="start FINA">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
            </bpmn:extensionElements>
            <bpmn:outgoing>Flow_1ijninc</bpmn:outgoing>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0rw5byv" escalationRef="Escalation_1502bkr" />
          </bpmn:startEvent>
          <bpmn:sequenceFlow id="Flow_1ijninc" sourceRef="Event_FINA" targetRef="Activity_stateNBDV" />
          <bpmn:serviceTask id="Activity_1y1fn3v" name="CallBuildingLoanApproval" camunda:type="external" camunda:topic="rekoCallApproval">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="processPhase">${processPhase}</camunda:inputParameter>
                <camunda:inputParameter name="processPart">${processPart}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1slf1ue</bpmn:incoming>
            <bpmn:outgoing>Flow_1gk5v5s</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_0zrdfrl" default="Flow_declined2">
            <bpmn:incoming>Flow_1gk5v5s</bpmn:incoming>
            <bpmn:outgoing>Flow_docGenApproval</bpmn:outgoing>
            <bpmn:outgoing>Flow_declined2</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:endEvent id="Event_callZDZA2" name="call ZDZA">
            <bpmn:incoming>Flow_declined2</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_170ouq6" escalationRef="Escalation_0sy21dp" />
          </bpmn:endEvent>
          <bpmn:endEvent id="Event_callDOCS" name="call DOCS">
            <bpmn:incoming>Flow_docGenApproval</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1rq93yu" escalationRef="Escalation_18s7skl" />
          </bpmn:endEvent>
          <bpmn:sequenceFlow id="Flow_1gk5v5s" sourceRef="Activity_1y1fn3v" targetRef="Gateway_0zrdfrl" />
          <bpmn:sequenceFlow id="Flow_docGenApproval" name="docGenApproval" sourceRef="Gateway_0zrdfrl" targetRef="Event_callDOCS">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="APPROVE"}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_declined2" name="declined" sourceRef="Gateway_0zrdfrl" targetRef="Event_callZDZA2" />
          <bpmn:sequenceFlow id="Flow_1slf1ue" sourceRef="Activity_SetApprovalParameters_2" targetRef="Activity_1y1fn3v" />
          <bpmn:subProcess id="Sub_applicationFinalized">
            <bpmn:incoming>Flow_1uurkko</bpmn:incoming>
            <bpmn:outgoing>Flow_0eealr6</bpmn:outgoing>
            <bpmn:startEvent id="Event_Start_applicationFinalized">
              <bpmn:outgoing>Flow_1mcxrwg</bpmn:outgoing>
            </bpmn:startEvent>
            <bpmn:endEvent id="Event_End_applicationFinalized">
              <bpmn:incoming>Flow_03xdlxq</bpmn:incoming>
            </bpmn:endEvent>
            <bpmn:intermediateCatchEvent id="Event_applicationFinalized" name="applicationFinalized">
              <bpmn:incoming>Flow_1mcxrwg</bpmn:incoming>
              <bpmn:outgoing>Flow_03xdlxq</bpmn:outgoing>
              <bpmn:messageEventDefinition id="MessageEventDefinition_02f4tbm" messageRef="Message_1jfelz5" />
            </bpmn:intermediateCatchEvent>
            <bpmn:sequenceFlow id="Flow_1mcxrwg" sourceRef="Event_Start_applicationFinalized" targetRef="Event_applicationFinalized" />
            <bpmn:sequenceFlow id="Flow_03xdlxq" sourceRef="Event_applicationFinalized" targetRef="Event_End_applicationFinalized" />
          </bpmn:subProcess>
          <bpmn:boundaryEvent id="Event_after15min" name="15m" cancelActivity="false" attachedToRef="Sub_applicationFinalized">
            <bpmn:outgoing>Flow_0h6m4i0</bpmn:outgoing>
            <bpmn:timerEventDefinition id="TimerEventDefinition_1gmwtey">
              <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT15M</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
          </bpmn:boundaryEvent>
          <bpmn:serviceTask id="Activity_getApplWithIncomes_finalization" name="getAppl with incomes" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="isIncome">1</camunda:inputParameter>
                <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
                <camunda:inputParameter name="requestedVariants">OFR</camunda:inputParameter>
                <camunda:inputParameter name="isObligation">0</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_isNotStrabag</bpmn:incoming>
            <bpmn:outgoing>Flow_0z0wdl9</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:endEvent id="Event_EndEmail********">
            <bpmn:incoming>Flow_0rbii3j</bpmn:incoming>
          </bpmn:endEvent>
          <bpmn:serviceTask id="Activity_generateEmail_********" name="generateEmail ********" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">********</camunda:inputParameter>
                <camunda:outputParameter name="email********Sent">true</camunda:outputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0z0wdl9</bpmn:incoming>
            <bpmn:outgoing>Flow_1w7ren3</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_0p6owbt">
            <bpmn:incoming>Flow_0y1jpk6</bpmn:incoming>
            <bpmn:incoming>Flow_isStrabag</bpmn:incoming>
            <bpmn:incoming>Flow_1ykv3nv</bpmn:incoming>
            <bpmn:outgoing>Flow_0rbii3j</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:serviceTask id="Activity_createSblActivity_********" name="Create SBL Activity ********" camunda:type="external" camunda:topic="rekoCreateSblActivity">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
              <camunda:inputOutput>
                <camunda:inputParameter name="activityStatus">Done</camunda:inputParameter>
                <camunda:inputParameter name="activityType">Email - Outbound</camunda:inputParameter>
                <camunda:inputParameter name="activityStart">${dateTime().toString()}</camunda:inputParameter>
                <camunda:inputParameter name="activityEnd" />
                <camunda:inputParameter name="description">email ******** - Reko schváleno</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1w7ren3</bpmn:incoming>
            <bpmn:outgoing>Flow_0y1jpk6</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_isStrabag" default="Flow_isNotStrabag">
            <bpmn:incoming>Flow_1rncl04</bpmn:incoming>
            <bpmn:outgoing>Flow_isNotStrabag</bpmn:outgoing>
            <bpmn:outgoing>Flow_isStrabag</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:exclusiveGateway id="Gateway_0riyl56" default="Flow_1rncl04">
            <bpmn:incoming>Flow_0h6m4i0</bpmn:incoming>
            <bpmn:outgoing>Flow_1rncl04</bpmn:outgoing>
            <bpmn:outgoing>Flow_1ykv3nv</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_isNotStrabag" sourceRef="Gateway_isStrabag" targetRef="Activity_getApplWithIncomes_finalization" />
          <bpmn:sequenceFlow id="Flow_0z0wdl9" sourceRef="Activity_getApplWithIncomes_finalization" targetRef="Activity_generateEmail_********" />
          <bpmn:sequenceFlow id="Flow_0rbii3j" sourceRef="Gateway_0p6owbt" targetRef="Event_EndEmail********" />
          <bpmn:sequenceFlow id="Flow_1w7ren3" sourceRef="Activity_generateEmail_********" targetRef="Activity_createSblActivity_********" />
          <bpmn:sequenceFlow id="Flow_0y1jpk6" sourceRef="Activity_createSblActivity_********" targetRef="Gateway_0p6owbt" />
          <bpmn:sequenceFlow id="Flow_isStrabag" sourceRef="Gateway_isStrabag" targetRef="Gateway_0p6owbt">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("loanApplication") != null &amp;&amp; execution.getVariable("loanApplication").promoCode != null &amp;&amp; execution.getVariable("loanApplication").promoCode.toUpperCase().contains("STRABAG")}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_1ykv3nv" name="email sent" sourceRef="Gateway_0riyl56" targetRef="Gateway_0p6owbt">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("email********Sent") == 'true'}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_1rncl04" name="email not sent" sourceRef="Gateway_0riyl56" targetRef="Gateway_isStrabag" />
          <bpmn:sequenceFlow id="Flow_0h6m4i0" sourceRef="Event_after15min" targetRef="Gateway_0riyl56" />
          <bpmn:serviceTask id="Activity_TriggerEmailNotification" name="Email notification NBDV" camunda:type="external" camunda:topic="rekoTriggerEmailNotification">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
                <camunda:inputParameter name="state">NBDV</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0xriihl</bpmn:incoming>
            <bpmn:outgoing>Flow_1uurkko</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1uurkko" sourceRef="Activity_TriggerEmailNotification" targetRef="Sub_applicationFinalized" />
        </bpmn:subProcess>
        <bpmn:subProcess id="Sub_disbursement" name="Disbursement" triggeredByEvent="true">
          <bpmn:serviceTask id="Activity_assignCategory" name="Assign Category" camunda:type="external" camunda:topic="rekoAssignCategory">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="type">PROSPECT_ORIGIN</camunda:inputParameter>
                <camunda:inputParameter name="categorization">REKO_PROCESS_APPROVED</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_03lpxog</bpmn:incoming>
            <bpmn:outgoing>Flow_0e4h8eo</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0e4h8eo" sourceRef="Activity_assignCategory" targetRef="Gateway_1dygd37" />
          <bpmn:parallelGateway id="Gateway_1dygd37">
            <bpmn:incoming>Flow_0e4h8eo</bpmn:incoming>
            <bpmn:incoming>Flow_1wklmsi</bpmn:incoming>
            <bpmn:incoming>Flow_0o8helq</bpmn:incoming>
            <bpmn:outgoing>Flow_197zmhs</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_197zmhs" sourceRef="Gateway_1dygd37" targetRef="Activity_transferDocs" />
          <bpmn:parallelGateway id="Gateway_0qd5rkz">
            <bpmn:incoming>Flow_1xhu69m</bpmn:incoming>
            <bpmn:outgoing>Flow_1t1xllm</bpmn:outgoing>
            <bpmn:outgoing>Flow_1k9gjax</bpmn:outgoing>
            <bpmn:outgoing>Flow_0rmmzcj</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_1t1xllm" sourceRef="Gateway_0qd5rkz" targetRef="Activity_generateDocumentX0048" />
          <bpmn:sequenceFlow id="Flow_1k9gjax" sourceRef="Gateway_0qd5rkz" targetRef="Activity_generateDocumentP1541" />
          <bpmn:sequenceFlow id="Flow_0rmmzcj" sourceRef="Gateway_0qd5rkz" targetRef="Activity_updateOpportunityDisb" />
          <bpmn:sequenceFlow id="Flow_139ic3e" sourceRef="Activity_transferDocs" targetRef="Activity_cibis_createProduct" />
          <bpmn:serviceTask id="Activity_createSblActivity" name="Create SBL Activity" camunda:type="external" camunda:topic="rekoCreateSblActivity">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
              <camunda:inputOutput>
                <camunda:inputParameter name="activityStatus">Done</camunda:inputParameter>
                <camunda:inputParameter name="activityType">Email - Outbound</camunda:inputParameter>
                <camunda:inputParameter name="activityStart">${dateTime().toString()}</camunda:inputParameter>
                <camunda:inputParameter name="activityEnd" />
                <camunda:inputParameter name="description">email X0000048 - REKO akceptováno</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1weia8x</bpmn:incoming>
            <bpmn:outgoing>Flow_1wklmsi</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1wklmsi" sourceRef="Activity_createSblActivity" targetRef="Gateway_1dygd37" />
          <bpmn:serviceTask id="Activity_generateDocumentX0048" name="generateEmail X0000048" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">X0000048</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1t1xllm</bpmn:incoming>
            <bpmn:outgoing>Flow_1weia8x</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1weia8x" sourceRef="Activity_generateDocumentX0048" targetRef="Activity_createSblActivity" />
          <bpmn:serviceTask id="Activity_generateDocumentP1541" name="Generate P0001541 - Akceptace" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001541</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1k9gjax</bpmn:incoming>
            <bpmn:outgoing>Flow_16oyx34</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_16oyx34" sourceRef="Activity_generateDocumentP1541" targetRef="Activity_setDocumentSmStateP0001541" />
          <bpmn:serviceTask id="Activity_updateOpportunityDisb" name="updateOpportunity" camunda:type="external" camunda:topic="rekoUpdateOpportunity">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="status">Win</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0rmmzcj</bpmn:incoming>
            <bpmn:outgoing>Flow_03lpxog</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_03lpxog" sourceRef="Activity_updateOpportunityDisb" targetRef="Activity_assignCategory" />
          <bpmn:serviceTask id="Activity_stateSMLAKCdisbursement" name="stateSMLAKC" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">SMLAKC</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0dqlu21</bpmn:incoming>
            <bpmn:outgoing>Flow_1xhu69m</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1xhu69m" sourceRef="Activity_stateSMLAKCdisbursement" targetRef="Gateway_0qd5rkz" />
          <bpmn:serviceTask id="Activity_getApplWithInc" name="getAppl with incomes" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="isIncome">1</camunda:inputParameter>
                <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
                <camunda:inputParameter name="isObligation">1</camunda:inputParameter>
                <camunda:inputParameter name="requestedVariants">APR,SEL</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0u97u7p</bpmn:incoming>
            <bpmn:outgoing>Flow_0dqlu21</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:endEvent id="Event_End_Disbursement" name="EndProcess">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0myerzk</bpmn:incoming>
          </bpmn:endEvent>
          <bpmn:startEvent id="Event_DISB" name="DISB">
            <bpmn:outgoing>Flow_0u97u7p</bpmn:outgoing>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1j9k49p" escalationRef="Escalation_1xujcvp" />
          </bpmn:startEvent>
          <bpmn:sequenceFlow id="Flow_0u97u7p" sourceRef="Event_DISB" targetRef="Activity_getApplWithInc" />
          <bpmn:serviceTask id="Activity_cibis_createProduct" name="Call CIBIS createProduct" camunda:type="external" camunda:topic="rekoSendKafkaCibisCreate">
            <bpmn:extensionElements />
            <bpmn:incoming>Flow_139ic3e</bpmn:incoming>
            <bpmn:outgoing>Flow_0myerzk</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0myerzk" sourceRef="Activity_cibis_createProduct" targetRef="Event_End_Disbursement" />
          <bpmn:sequenceFlow id="Flow_0dqlu21" sourceRef="Activity_getApplWithInc" targetRef="Activity_stateSMLAKCdisbursement" />
          <bpmn:serviceTask id="Activity_transferDocs" name="Transfer docs" camunda:type="external" camunda:topic="rekoTransferDocs">
            <bpmn:incoming>Flow_197zmhs</bpmn:incoming>
            <bpmn:outgoing>Flow_139ic3e</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_setDocumentSmStateP0001541" name="Set SmState CLNTF_PREPUBLISH on P0001541" camunda:type="external" camunda:topic="rekoSetDocumentSmState">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001541</camunda:inputParameter>
                <camunda:inputParameter name="smState">CLNTF_PREPUBLISH</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_16oyx34</bpmn:incoming>
            <bpmn:outgoing>Flow_0o8helq</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0o8helq" sourceRef="Activity_setDocumentSmStateP0001541" targetRef="Gateway_1dygd37" />
        </bpmn:subProcess>
        <bpmn:subProcess id="Sub_DocGeneration" name="Doc generation" triggeredByEvent="true">
          <bpmn:serviceTask id="Activity_calculateBuildingLoanIPS" name="Calculate Building Loan IPS" camunda:type="external" camunda:topic="rekoCalculateBuildingLoanIPS">
            <bpmn:incoming>Flow_0im42v1</bpmn:incoming>
            <bpmn:outgoing>Flow_193am9z</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_stateSMLPOD" name="stateSMLPOD" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">SMLPOD</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1m6javp</bpmn:incoming>
            <bpmn:outgoing>Flow_1fcu878</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_stateSMLG" name="stateSMLG" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">SMLG</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1rk0aow</bpmn:incoming>
            <bpmn:outgoing>Flow_1wiheae</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_getAppl" name="getAppl with incomes" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="isIncome">1</camunda:inputParameter>
                <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
                <camunda:inputParameter name="isObligation">1</camunda:inputParameter>
                <camunda:inputParameter name="requestedVariants">SEL,APR</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0p1cg95</bpmn:incoming>
            <bpmn:outgoing>Flow_0yixis5</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_stateZDSCH" name="stateZDSCH" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">ZDSCH</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1a2pgzt</bpmn:incoming>
            <bpmn:outgoing>Flow_07tc4s5</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_07tc4s5" sourceRef="Activity_stateZDSCH" targetRef="Gateway_docGenerationJoin" />
          <bpmn:intermediateCatchEvent id="Event_onbStatusChange" name="onbStatusChange">
            <bpmn:incoming>Flow_0r7mw3f</bpmn:incoming>
            <bpmn:outgoing>Flow_0gw7e9s</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1y0wgs0" messageRef="Message_37ikigs" />
          </bpmn:intermediateCatchEvent>
          <bpmn:serviceTask id="Activity_setSigningChannel_81" name="set signing channel 81" camunda:type="external" camunda:topic="rekoSetSigningChannel">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="signingChannel">81</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_FINISHED</bpmn:incoming>
            <bpmn:outgoing>Flow_0t2ixmt</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0gw7e9s" sourceRef="Event_onbStatusChange" targetRef="Gateway_03k5116" />
          <bpmn:serviceTask id="Activity_ReGenerateDocument_P0001540" name="Generate P0001540 - Návrh Smlouvy" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001540 - Návrh Smlouvy o stavebním spoření, Smlouvy o úvěru a Zástavní smlouvy</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001540</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_13e7ck9</bpmn:incoming>
            <bpmn:outgoing>Flow_1j4wjzq</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_ReGenerateDocument_P0001543" name="Generate P0001543 - Předsmluvní podmínky" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001543 - Předsmluvní informace</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001543</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0qwopln</bpmn:incoming>
            <bpmn:outgoing>Flow_13e7ck9</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_13e7ck9" sourceRef="Activity_ReGenerateDocument_P0001543" targetRef="Activity_ReGenerateDocument_P0001540" />
          <bpmn:sequenceFlow id="Flow_193am9z" sourceRef="Activity_calculateBuildingLoanIPS" targetRef="Activity_getSBLPartyDetail" />
          <bpmn:serviceTask id="Activity_GenerateDocument_P0001540" name="Generate P0001540 - Návrh Smlouvy" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001540 - Návrh Smlouvy o stavebním spoření, Smlouvy o úvěru a Zástavní smlouvy</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001540</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_13hckw7</bpmn:incoming>
            <bpmn:outgoing>Flow_0xuc1tz</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_setSigningChannel_10" name="set signing channel 10" camunda:type="external" camunda:topic="rekoSetSigningChannel">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="signingChannel">10</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1jfh5d2</bpmn:incoming>
            <bpmn:outgoing>Flow_1aq4xth</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_join3">
            <bpmn:incoming>Flow_0t2ixmt</bpmn:incoming>
            <bpmn:incoming>Flow_0ov9wku</bpmn:incoming>
            <bpmn:incoming>Flow_0gb9jck</bpmn:incoming>
            <bpmn:outgoing>Flow_0ivoyq8</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_0ivoyq8" sourceRef="Gateway_join3" targetRef="Activity_setContractSigned" />
          <bpmn:sequenceFlow id="Flow_0t2ixmt" sourceRef="Activity_setSigningChannel_81" targetRef="Gateway_join3" />
          <bpmn:sequenceFlow id="Flow_1aq4xth" sourceRef="Activity_setSigningChannel_10" targetRef="Activity_update_signChannel" />
          <bpmn:intermediateCatchEvent id="Event_clContractSigned_branch" name="clContractSigned_branch">
            <bpmn:incoming>Flow_1kt9xg4</bpmn:incoming>
            <bpmn:outgoing>Flow_04grr9t</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1j0vjum" messageRef="Message_0vr4ul4" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_04grr9t" sourceRef="Event_clContractSigned_branch" targetRef="Gateway_192486p" />
          <bpmn:subProcess id="Sub_clientApproval">
            <bpmn:incoming>Flow_0livisq</bpmn:incoming>
            <bpmn:outgoing>Flow_0a9th1e</bpmn:outgoing>
            <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${approvalDocuments.documents}" camunda:elementVariable="approvalDocument" />
            <bpmn:serviceTask id="Activity_addClientApproval" name="add Client Approval" camunda:type="external" camunda:topic="rekoAddClientApproval">
              <bpmn:incoming>Flow_050paj9</bpmn:incoming>
              <bpmn:outgoing>Flow_0mzledh</bpmn:outgoing>
            </bpmn:serviceTask>
            <bpmn:endEvent id="Event_End_clientApproval">
              <bpmn:incoming>Flow_0mzledh</bpmn:incoming>
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0mzledh" sourceRef="Activity_addClientApproval" targetRef="Event_End_clientApproval" />
            <bpmn:startEvent id="Event_Start_clientApproval">
              <bpmn:outgoing>Flow_050paj9</bpmn:outgoing>
            </bpmn:startEvent>
            <bpmn:sequenceFlow id="Flow_050paj9" sourceRef="Event_Start_clientApproval" targetRef="Activity_addClientApproval" />
          </bpmn:subProcess>
          <bpmn:sequenceFlow id="Flow_0a9th1e" sourceRef="Sub_clientApproval" targetRef="Activity_signing_channel_15" />
          <bpmn:serviceTask id="Activity_setContractSigned" name="setContractSigned" camunda:type="external" camunda:topic="rekoSetContractSigned">
            <bpmn:incoming>Flow_0ivoyq8</bpmn:incoming>
            <bpmn:outgoing>Flow_1m6javp</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1m6javp" sourceRef="Activity_setContractSigned" targetRef="Activity_stateSMLPOD" />
          <bpmn:serviceTask id="Activity_GenerateDocument_P0001543" name="Generate P0001543 - Předsmluvní podmínky" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001543 - Předsmluvní informace</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001543</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04gveby</bpmn:incoming>
            <bpmn:outgoing>Flow_01hrxq5</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:endEvent id="Event_10t4fmz" name="call ZDZA">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_030ajgk</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0r27b19" escalationRef="Escalation_0sy21dp" />
          </bpmn:endEvent>
          <bpmn:exclusiveGateway id="Gateway_docGenerationJoin">
            <bpmn:incoming>Flow_07tc4s5</bpmn:incoming>
            <bpmn:incoming>Flow_1rp3vkn</bpmn:incoming>
            <bpmn:outgoing>Flow_0p1cg95</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_0p1cg95" sourceRef="Gateway_docGenerationJoin" targetRef="Activity_getAppl" />
          <bpmn:intermediateCatchEvent id="Event_contractSignedOnline" name="contractSignedOnline">
            <bpmn:incoming>Flow_09sbjzx</bpmn:incoming>
            <bpmn:outgoing>Flow_0livisq</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_0stcc35" messageRef="Message_16mguw7" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_0livisq" sourceRef="Event_contractSignedOnline" targetRef="Sub_clientApproval" />
          <bpmn:eventBasedGateway id="Gateway_waitForEvent">
            <bpmn:incoming>Flow_1wiheae</bpmn:incoming>
            <bpmn:incoming>Flow_1u5rgte</bpmn:incoming>
            <bpmn:outgoing>Flow_0r7mw3f</bpmn:outgoing>
            <bpmn:outgoing>Flow_1kt9xg4</bpmn:outgoing>
            <bpmn:outgoing>Flow_09sbjzx</bpmn:outgoing>
            <bpmn:outgoing>Flow_1laad81</bpmn:outgoing>
            <bpmn:outgoing>Flow_0k4w8ni</bpmn:outgoing>
            <bpmn:outgoing>Flow_1dxi3t3</bpmn:outgoing>
          </bpmn:eventBasedGateway>
          <bpmn:sequenceFlow id="Flow_0r7mw3f" sourceRef="Gateway_waitForEvent" targetRef="Event_onbStatusChange" />
          <bpmn:sequenceFlow id="Flow_1kt9xg4" sourceRef="Gateway_waitForEvent" targetRef="Event_clContractSigned_branch" />
          <bpmn:sequenceFlow id="Flow_09sbjzx" sourceRef="Gateway_waitForEvent" targetRef="Event_contractSignedOnline" />
          <bpmn:sequenceFlow id="Flow_1wiheae" sourceRef="Activity_stateSMLG" targetRef="Gateway_waitForEvent" />
          <bpmn:intermediateCatchEvent id="Event_applicationFinalized2" name="applicationFinalized">
            <bpmn:incoming>Flow_1laad81</bpmn:incoming>
            <bpmn:outgoing>Flow_09et9k2</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_08bk2qs" messageRef="Message_1jfelz5" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_1laad81" sourceRef="Gateway_waitForEvent" targetRef="Event_applicationFinalized2" />
          <bpmn:exclusiveGateway id="Gateway_approvalResult" name="result">
            <bpmn:incoming>Flow_0l370yf</bpmn:incoming>
            <bpmn:outgoing>Flow_030ajgk</bpmn:outgoing>
            <bpmn:outgoing>Flow_1rp3vkn</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_030ajgk" name="rejected" sourceRef="Gateway_approvalResult" targetRef="Event_10t4fmz">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult!='APPROVE'}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_09et9k2" sourceRef="Event_applicationFinalized2" targetRef="Activity_callApproval_1st_scoring_only" />
          <bpmn:exclusiveGateway id="Gateway_1stRound" default="Flow_0gsqyg2">
            <bpmn:incoming>Flow_0rdllvi</bpmn:incoming>
            <bpmn:outgoing>Flow_0gsqyg2</bpmn:outgoing>
            <bpmn:outgoing>Flow_1wdu5k4</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_0gsqyg2" name="&#62;1st round of generation" sourceRef="Gateway_1stRound" targetRef="Activity_prepareDocumentUpdate" />
          <bpmn:sequenceFlow id="Flow_1wdu5k4" name="1st round of generation" sourceRef="Gateway_1stRound" targetRef="Gateway_parallel_1">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ !(execution.getVariable('generatedDocuments') != null &amp;&amp; execution.getVariable('generatedDocuments').getGeneratedObjectIds() != null &amp;&amp; !execution.getVariable('generatedDocuments').getGeneratedObjectIds().isEmpty()) }</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:exclusiveGateway id="Gateway_docGenerationJoin3">
            <bpmn:incoming>Flow_0y60ai6</bpmn:incoming>
            <bpmn:incoming>Flow_03uzsso</bpmn:incoming>
            <bpmn:outgoing>Flow_1rk0aow</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_1rk0aow" sourceRef="Gateway_docGenerationJoin3" targetRef="Activity_stateSMLG" />
          <bpmn:sequenceFlow id="Flow_01hrxq5" sourceRef="Activity_GenerateDocument_P0001543" targetRef="Activity_setDocumentSmStateP0001543" />
          <bpmn:sequenceFlow id="Flow_0yixis5" sourceRef="Activity_getAppl" targetRef="Activity_getDealerInfo" />
          <bpmn:startEvent id="Event_DOCS" name="start DOCS">
            <bpmn:outgoing>Flow_1a2pgzt</bpmn:outgoing>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1kuzw69" escalationRef="Escalation_18s7skl" />
          </bpmn:startEvent>
          <bpmn:sequenceFlow id="Flow_1a2pgzt" sourceRef="Event_DOCS" targetRef="Activity_stateZDSCH" />
          <bpmn:serviceTask id="Activity_getDealerInfo" name="Get Dealer Info" camunda:type="external" camunda:topic="rekoGetDealerInfo">
            <bpmn:incoming>Flow_0yixis5</bpmn:incoming>
            <bpmn:outgoing>Flow_0im42v1</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0im42v1" sourceRef="Activity_getDealerInfo" targetRef="Activity_calculateBuildingLoanIPS" />
          <bpmn:serviceTask id="Activity_getSBLPartyDetail" name="getSBLPartyDetail" camunda:type="external" camunda:topic="rekoGetSBLPartyDetail">
            <bpmn:extensionElements />
            <bpmn:incoming>Flow_193am9z</bpmn:incoming>
            <bpmn:outgoing>Flow_0rdllvi</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0rdllvi" sourceRef="Activity_getSBLPartyDetail" targetRef="Gateway_1stRound" />
          <bpmn:serviceTask id="Activity_callApproval_1st_scoring_only" name="callApproval (1st scoring only)" camunda:type="external" camunda:topic="rekoCallApproval">
            <bpmn:extensionElements>
              <camunda:field name="processPhase">
                <camunda:string>3</camunda:string>
              </camunda:field>
              <camunda:field name="processPart">
                <camunda:string>KO_PRE_CONTRACT_GEN</camunda:string>
              </camunda:field>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_09et9k2</bpmn:incoming>
            <bpmn:outgoing>Flow_0l370yf</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0l370yf" sourceRef="Activity_callApproval_1st_scoring_only" targetRef="Gateway_approvalResult" />
          <bpmn:sequenceFlow id="Flow_1rp3vkn" name="approved" sourceRef="Gateway_approvalResult" targetRef="Gateway_docGenerationJoin">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
            </bpmn:extensionElements>
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=='APPROVE'}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:serviceTask id="Activity_callBuildingLoanApproval_2" name="CallBuildingLoanApproval" camunda:type="external" camunda:topic="rekoCallApproval">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="processPhase">${processPhase}</camunda:inputParameter>
                <camunda:inputParameter name="processPart">${processPart}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fcu878</bpmn:incoming>
            <bpmn:outgoing>Flow_1l4mqoq</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1fcu878" sourceRef="Activity_stateSMLPOD" targetRef="Activity_callBuildingLoanApproval_2" />
          <bpmn:exclusiveGateway id="Gateway_0t5ksiv" default="Flow_declined3">
            <bpmn:incoming>Flow_1l4mqoq</bpmn:incoming>
            <bpmn:outgoing>Event_callDISB</bpmn:outgoing>
            <bpmn:outgoing>Flow_declined3</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:endEvent id="Event_callDISB2" name="call DISB">
            <bpmn:incoming>Event_callDISB</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0iuqe9a" escalationRef="Escalation_1xujcvp" />
          </bpmn:endEvent>
          <bpmn:sequenceFlow id="Event_callDISB" name="docGenApproval" sourceRef="Gateway_0t5ksiv" targetRef="Event_callDISB2">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="APPROVE"}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:endEvent id="Event_callZDZA3" name="call ZDZA">
            <bpmn:incoming>Flow_declined3</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1gvdhxb" escalationRef="Escalation_0sy21dp" />
          </bpmn:endEvent>
          <bpmn:sequenceFlow id="Flow_1l4mqoq" sourceRef="Activity_callBuildingLoanApproval_2" targetRef="Gateway_0t5ksiv" />
          <bpmn:sequenceFlow id="Flow_declined3" name="declined" sourceRef="Gateway_0t5ksiv" targetRef="Event_callZDZA3" />
          <bpmn:sequenceFlow id="Flow_0xuc1tz" sourceRef="Activity_GenerateDocument_P0001540" targetRef="Gateway_join_parallel_1" />
          <bpmn:sequenceFlow id="Flow_1j4wjzq" sourceRef="Activity_ReGenerateDocument_P0001540" targetRef="Gateway_join_parallel_2" />
          <bpmn:intermediateCatchEvent id="Event_clContractSigned_dms" name="clContractSigned_dms">
            <bpmn:incoming>Flow_0k4w8ni</bpmn:incoming>
            <bpmn:outgoing>Flow_1xq70od</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_02yr23o" messageRef="Message_0aqmleu" />
          </bpmn:intermediateCatchEvent>
          <bpmn:exclusiveGateway id="Gateway_192486p">
            <bpmn:incoming>Flow_04grr9t</bpmn:incoming>
            <bpmn:incoming>Flow_1xq70od</bpmn:incoming>
            <bpmn:outgoing>Flow_1jfh5d2</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_1jfh5d2" sourceRef="Gateway_192486p" targetRef="Activity_setSigningChannel_10" />
          <bpmn:sequenceFlow id="Flow_0k4w8ni" sourceRef="Gateway_waitForEvent" targetRef="Event_clContractSigned_dms" />
          <bpmn:sequenceFlow id="Flow_1xq70od" sourceRef="Event_clContractSigned_dms" targetRef="Gateway_192486p" />
          <bpmn:serviceTask id="Activity_update_signChannel" name="update sign channel" camunda:type="external" camunda:topic="rekoUpdateSignChannel">
            <bpmn:incoming>Flow_1aq4xth</bpmn:incoming>
            <bpmn:outgoing>Flow_0gb9jck</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0gb9jck" sourceRef="Activity_update_signChannel" targetRef="Gateway_join3" />
          <bpmn:serviceTask id="Activity_signing_channel_15" name="set signing channel 15" camunda:type="external" camunda:topic="rekoSetSigningChannel">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="signingChannel">15</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0a9th1e</bpmn:incoming>
            <bpmn:outgoing>Flow_0ov9wku</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0ov9wku" sourceRef="Activity_signing_channel_15" targetRef="Gateway_join3" />
          <bpmn:intermediateCatchEvent id="Event_onbChannelChange" name="onbChannelChange">
            <bpmn:incoming>Flow_1dxi3t3</bpmn:incoming>
            <bpmn:outgoing>Flow_0ctow30</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1d3tbha" messageRef="Message_25b4vlo" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_1dxi3t3" sourceRef="Gateway_waitForEvent" targetRef="Event_onbChannelChange" />
          <bpmn:serviceTask id="Activity_updateChannel" name="Update channel" camunda:type="external" camunda:topic="rekoUpdateSigningChannel">
            <bpmn:incoming>Flow_0ctow30</bpmn:incoming>
            <bpmn:outgoing>Flow_04mqc5y</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0ctow30" sourceRef="Event_onbChannelChange" targetRef="Activity_updateChannel" />
          <bpmn:exclusiveGateway id="Gateway_03k5116" name="state from incomming message">
            <bpmn:incoming>Flow_0gw7e9s</bpmn:incoming>
            <bpmn:outgoing>Flow_FINISHED</bpmn:outgoing>
            <bpmn:outgoing>Flow_REJECTED_CANCELED</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:serviceTask id="Activity_channel10" name="set signing channel 10" camunda:type="external" camunda:topic="rekoSetSigningChannel">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="signingChannel">10</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_REJECTED_CANCELED</bpmn:incoming>
            <bpmn:outgoing>Flow_1fkb6wr</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_FINISHED" name="state = FINISHED" sourceRef="Gateway_03k5116" targetRef="Activity_setSigningChannel_81">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${onbStatusChange_applicationState=='FINISHED'}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_REJECTED_CANCELED" name="state = REJECTED or CANCELED" sourceRef="Gateway_03k5116" targetRef="Activity_channel10">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(onbStatusChange_applicationState=='REJECTED') or (onbStatusChange_applicationState=='CANCELED')}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:exclusiveGateway id="Gateway_join_9o">
            <bpmn:incoming>Flow_1fkb6wr</bpmn:incoming>
            <bpmn:incoming>Flow_04mqc5y</bpmn:incoming>
            <bpmn:outgoing>Flow_1o7smm3</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_1fkb6wr" sourceRef="Activity_channel10" targetRef="Gateway_join_9o" />
          <bpmn:sequenceFlow id="Flow_04mqc5y" sourceRef="Activity_updateChannel" targetRef="Gateway_join_9o" />
          <bpmn:serviceTask id="Activity_update_signChannel2" name="update sign channel" camunda:type="external" camunda:topic="rekoUpdateSignChannel">
            <bpmn:incoming>Flow_1o7smm3</bpmn:incoming>
            <bpmn:outgoing>Flow_1u5rgte</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1o7smm3" sourceRef="Gateway_join_9o" targetRef="Activity_update_signChannel2" />
          <bpmn:sequenceFlow id="Flow_1u5rgte" sourceRef="Activity_update_signChannel2" targetRef="Gateway_waitForEvent" />
          <bpmn:serviceTask id="Activity_prepareDocumentUpdate" name="prepare document update" camunda:type="external" camunda:topic="rekoPrepareDocumentUpdate">
            <bpmn:incoming>Flow_0gsqyg2</bpmn:incoming>
            <bpmn:outgoing>Flow_0dkc8ux</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0dkc8ux" sourceRef="Activity_prepareDocumentUpdate" targetRef="Gateway_parallel_2" />
          <bpmn:serviceTask id="Activity_setDocumentSmStateP0001543" name="Set SmState CLNTF_PREPUBLISH on P0001543 " camunda:type="external" camunda:topic="rekoSetDocumentSmState">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001543</camunda:inputParameter>
                <camunda:inputParameter name="smState">CLNTF_PREPUBLISH</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_01hrxq5</bpmn:incoming>
            <bpmn:outgoing>Flow_13hckw7</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_13hckw7" sourceRef="Activity_setDocumentSmStateP0001543" targetRef="Activity_GenerateDocument_P0001540" />
          <bpmn:serviceTask id="Activity_generate_P0001644_1" name="Generate P0001644 - Záznam z jednání" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001644 - Záznam z jednání</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001644</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1k14tyi</bpmn:incoming>
            <bpmn:outgoing>Flow_1hviacv</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_Generate_P0001644_2" name="Generate P0001644 - Záznam z jednání" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>P0001644 - Záznam z jednání</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001644</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_insurance_confirmed</bpmn:incoming>
            <bpmn:outgoing>Flow_12qw6ei</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_delete_P0001644" name="Delete P0001644 - Záznam z jednání" camunda:type="external" camunda:topic="rekoDeleteDocumentByTemplateId">
            <bpmn:documentation>P0001644 - Záznam z jednání</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">P0001644</camunda:inputParameter>
                <camunda:inputParameter name="supressErrorAndContinue">YES</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_insurance_removed</bpmn:incoming>
            <bpmn:outgoing>Flow_129deap</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_insuranceConfirmed" default="Flow_insurance_confirmed">
            <bpmn:incoming>Flow_0srfs28</bpmn:incoming>
            <bpmn:outgoing>Flow_insurance_confirmed</bpmn:outgoing>
            <bpmn:outgoing>Flow_insurance_removed</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:exclusiveGateway id="Gateway_join_insuranceConfirmed">
            <bpmn:incoming>Flow_12qw6ei</bpmn:incoming>
            <bpmn:incoming>Flow_129deap</bpmn:incoming>
            <bpmn:outgoing>Flow_00a1jo4</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_04gveby" sourceRef="Gateway_parallel_1" targetRef="Activity_GenerateDocument_P0001543" />
          <bpmn:sequenceFlow id="Flow_fghabj8" sourceRef="Gateway_parallel_1" targetRef="Gateway_selectedInsurance" />
          <bpmn:parallelGateway id="Gateway_parallel_1">
            <bpmn:incoming>Flow_1wdu5k4</bpmn:incoming>
            <bpmn:outgoing>Flow_04gveby</bpmn:outgoing>
            <bpmn:outgoing>Flow_fghabj8</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:parallelGateway id="Gateway_join_parallel_1">
            <bpmn:incoming>Flow_0xuc1tz</bpmn:incoming>
            <bpmn:incoming>Flow_12hjv42</bpmn:incoming>
            <bpmn:outgoing>Flow_0y60ai6</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_0y60ai6" sourceRef="Gateway_join_parallel_1" targetRef="Gateway_docGenerationJoin3" />
          <bpmn:sequenceFlow id="Flow_1hviacv" sourceRef="Activity_generate_P0001644_1" targetRef="Gateway_join_selectedInsurance" />
          <bpmn:parallelGateway id="Gateway_parallel_2">
            <bpmn:incoming>Flow_0dkc8ux</bpmn:incoming>
            <bpmn:outgoing>Flow_0qwopln</bpmn:outgoing>
            <bpmn:outgoing>Flow_0srfs28</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_0qwopln" sourceRef="Gateway_parallel_2" targetRef="Activity_ReGenerateDocument_P0001543" />
          <bpmn:sequenceFlow id="Flow_insurance_confirmed" name="insurance confirmed" sourceRef="Gateway_insuranceConfirmed" targetRef="Activity_Generate_P0001644_2" />
          <bpmn:sequenceFlow id="Flow_insurance_removed" name="insurance removed" sourceRef="Gateway_insuranceConfirmed" targetRef="Activity_delete_P0001644">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${insuranceRemoved}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_0srfs28" sourceRef="Gateway_parallel_2" targetRef="Gateway_insuranceConfirmed" />
          <bpmn:parallelGateway id="Gateway_join_parallel_2">
            <bpmn:incoming>Flow_1j4wjzq</bpmn:incoming>
            <bpmn:incoming>Flow_00a1jo4</bpmn:incoming>
            <bpmn:outgoing>Flow_03uzsso</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_03uzsso" sourceRef="Gateway_join_parallel_2" targetRef="Gateway_docGenerationJoin3" />
          <bpmn:sequenceFlow id="Flow_00a1jo4" sourceRef="Gateway_join_insuranceConfirmed" targetRef="Gateway_join_parallel_2" />
          <bpmn:sequenceFlow id="Flow_12qw6ei" sourceRef="Activity_Generate_P0001644_2" targetRef="Gateway_join_insuranceConfirmed" />
          <bpmn:sequenceFlow id="Flow_129deap" sourceRef="Activity_delete_P0001644" targetRef="Gateway_join_insuranceConfirmed" />
          <bpmn:exclusiveGateway id="Gateway_selectedInsurance" default="Flow_0r3z0ow">
            <bpmn:incoming>Flow_fghabj8</bpmn:incoming>
            <bpmn:outgoing>Flow_1k14tyi</bpmn:outgoing>
            <bpmn:outgoing>Flow_0r3z0ow</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_1k14tyi" name="insurance selected" sourceRef="Gateway_selectedInsurance" targetRef="Activity_generate_P0001644_1">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${insuranceSelected}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:exclusiveGateway id="Gateway_join_selectedInsurance">
            <bpmn:incoming>Flow_1hviacv</bpmn:incoming>
            <bpmn:incoming>Flow_0r3z0ow</bpmn:incoming>
            <bpmn:outgoing>Flow_12hjv42</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_12hjv42" sourceRef="Gateway_join_selectedInsurance" targetRef="Gateway_join_parallel_1" />
          <bpmn:sequenceFlow id="Flow_0r3z0ow" sourceRef="Gateway_selectedInsurance" targetRef="Gateway_join_selectedInsurance" />
        </bpmn:subProcess>
        <bpmn:subProcess id="Sub_Approval" name="Approval" triggeredByEvent="true">
          <bpmn:endEvent id="Event_callZDZA" name="call ZDZA">
            <bpmn:incoming>Flow_declined</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_18sy31i" escalationRef="Escalation_0sy21dp" />
          </bpmn:endEvent>
          <bpmn:endEvent id="Event_callFINA" name="call FINA">
            <bpmn:incoming>Flow_approved</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1o1ht9w" escalationRef="Escalation_1502bkr" />
          </bpmn:endEvent>
          <bpmn:endEvent id="Event_callZDST" name="call ZDST">
            <bpmn:incoming>Flow_1cjp8mr</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1b93azh" escalationRef="Escalation_14sedn4" />
          </bpmn:endEvent>
          <bpmn:serviceTask id="Activity_CallBuildingLoanApproval" name="CallBuildingLoanApproval" camunda:type="external" camunda:topic="rekoCallApproval">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="processPhase">${processPhase}</camunda:inputParameter>
                <camunda:inputParameter name="processPart">${processPart}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1h7e9kg</bpmn:incoming>
            <bpmn:incoming>Flow_0pp85dv</bpmn:incoming>
            <bpmn:outgoing>Flow_0l4dxtx</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_1lospgx" default="Flow_0rijd0k">
            <bpmn:incoming>Flow_0l4dxtx</bpmn:incoming>
            <bpmn:outgoing>Flow_declined</bpmn:outgoing>
            <bpmn:outgoing>Flow_approved</bpmn:outgoing>
            <bpmn:outgoing>Flow_0rijd0k</bpmn:outgoing>
            <bpmn:outgoing>Flow_043w1ws</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_declined" name="declined" sourceRef="Gateway_1lospgx" targetRef="Event_callZDZA">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="DECLINE"}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_approved" name="approved" sourceRef="Gateway_1lospgx" targetRef="Event_callFINA">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="APPROVE"}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_0l4dxtx" sourceRef="Activity_CallBuildingLoanApproval" targetRef="Gateway_1lospgx" />
          <bpmn:eventBasedGateway id="Gateway_waitForEvent2">
            <bpmn:incoming>Flow_10iym5i</bpmn:incoming>
            <bpmn:outgoing>Flow_1mhdtya</bpmn:outgoing>
            <bpmn:outgoing>Flow_0wxfl2o</bpmn:outgoing>
          </bpmn:eventBasedGateway>
          <bpmn:sequenceFlow id="Flow_0rijd0k" name="manualApprovalRequired" sourceRef="Gateway_1lospgx" targetRef="Activity_StateMSCH1">
            <bpmn:documentation>default:
VERIFYREQ
ACCEPTREQ
FRDCHCKREQ
SYSTASKREQ</bpmn:documentation>
          </bpmn:sequenceFlow>
          <bpmn:serviceTask id="Activity_createManualTask" name="createManualTask" camunda:type="external" camunda:topic="rekoCreateManualTask">
            <bpmn:incoming>Flow_0yze336</bpmn:incoming>
            <bpmn:outgoing>Flow_10iym5i</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_10iym5i" sourceRef="Activity_createManualTask" targetRef="Gateway_waitForEvent2" />
          <bpmn:serviceTask id="Activity_StateMSCH1" name="state MSCH1" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">MSCH1</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0rijd0k</bpmn:incoming>
            <bpmn:outgoing>Flow_0yze336</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0yze336" sourceRef="Activity_StateMSCH1" targetRef="Activity_createManualTask" />
          <bpmn:serviceTask id="Activity_state_ZDDEKL" name="state ZDDEKL" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">ZDDEKL</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_043w1ws</bpmn:incoming>
            <bpmn:outgoing>Flow_0ka6vwr</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_043w1ws" name="declarationRequired" sourceRef="Gateway_1lospgx" targetRef="Activity_state_ZDDEKL">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="DECLARE"}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:serviceTask id="Activity_state_ZDDOKL" name="state ZDDOKL" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">ZDDOKL</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1u3ysbp</bpmn:incoming>
            <bpmn:outgoing>Flow_1ubvlxr</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0ka6vwr" sourceRef="Activity_state_ZDDEKL" targetRef="Event_purposeDeclared" />
          <bpmn:startEvent id="Event_APPR" name="APPR">
            <bpmn:outgoing>Flow_1h7e9kg</bpmn:outgoing>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1uu34j0" escalationRef="Escalation_1mm4trv" camunda:escalationCodeVariable="APPR" />
          </bpmn:startEvent>
          <bpmn:sequenceFlow id="Flow_1h7e9kg" sourceRef="Event_APPR" targetRef="Activity_CallBuildingLoanApproval" />
          <bpmn:exclusiveGateway id="Gateway_16mgwhc">
            <bpmn:incoming>Flow_0wy61x9</bpmn:incoming>
            <bpmn:incoming>Flow_0cl9dir</bpmn:incoming>
            <bpmn:outgoing>Flow_04kphzc</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_04kphzc" sourceRef="Gateway_16mgwhc" targetRef="Activity_state_ZDPDP" />
          <bpmn:sequenceFlow id="Flow_1ubvlxr" sourceRef="Activity_state_ZDDOKL" targetRef="Sub_docsUploaded" />
          <bpmn:intermediateCatchEvent id="Event_manualTaskDone" name="manualTaskDone">
            <bpmn:incoming>Flow_1mhdtya</bpmn:incoming>
            <bpmn:outgoing>Flow_0wy61x9</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_14ju9c5" messageRef="Message_2epvd2k" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_0wy61x9" sourceRef="Event_manualTaskDone" targetRef="Gateway_16mgwhc" />
          <bpmn:sequenceFlow id="Flow_1mhdtya" sourceRef="Gateway_waitForEvent2" targetRef="Event_manualTaskDone" />
          <bpmn:intermediateCatchEvent id="Event_purposeDeclared" name="purposeDeclared">
            <bpmn:incoming>Flow_0ka6vwr</bpmn:incoming>
            <bpmn:outgoing>Flow_1u3ysbp</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1swh7mt" messageRef="Message_2nm4rek" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_1u3ysbp" sourceRef="Event_purposeDeclared" targetRef="Activity_state_ZDDOKL" />
          <bpmn:intermediateCatchEvent id="Event_manualTaskCanceled" name="manualTaskCanceled">
            <bpmn:incoming>Flow_0wxfl2o</bpmn:incoming>
            <bpmn:outgoing>Flow_1cjp8mr</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_17bsikn" messageRef="Message_3spdfar" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_1cjp8mr" sourceRef="Event_manualTaskCanceled" targetRef="Event_callZDST" />
          <bpmn:sequenceFlow id="Flow_0wxfl2o" sourceRef="Gateway_waitForEvent2" targetRef="Event_manualTaskCanceled" />
          <bpmn:sequenceFlow id="Flow_0cl9dir" sourceRef="Sub_docsUploaded" targetRef="Gateway_16mgwhc" />
          <bpmn:serviceTask id="Activity_state_ZDPDP" name="state ZDPDP" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">ZDPDP</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04kphzc</bpmn:incoming>
            <bpmn:outgoing>Flow_0pp85dv</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_0pp85dv" sourceRef="Activity_state_ZDPDP" targetRef="Activity_CallBuildingLoanApproval" />
          <bpmn:subProcess id="Sub_docsUploaded">
            <bpmn:incoming>Flow_1ubvlxr</bpmn:incoming>
            <bpmn:outgoing>Flow_0cl9dir</bpmn:outgoing>
            <bpmn:startEvent id="Event_Start_docsUploaded">
              <bpmn:outgoing>Flow_188z0ss</bpmn:outgoing>
            </bpmn:startEvent>
            <bpmn:intermediateCatchEvent id="Event_docsUploaded" name="docsUploaded">
              <bpmn:incoming>Flow_188z0ss</bpmn:incoming>
              <bpmn:outgoing>Flow_0ec1tje</bpmn:outgoing>
              <bpmn:messageEventDefinition id="MessageEventDefinition_03pgor7" messageRef="Message_3ofsmu0" />
            </bpmn:intermediateCatchEvent>
            <bpmn:sequenceFlow id="Flow_188z0ss" sourceRef="Event_Start_docsUploaded" targetRef="Event_docsUploaded" />
            <bpmn:endEvent id="Event_End_docsUploaded">
              <bpmn:incoming>Flow_0ec1tje</bpmn:incoming>
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0ec1tje" sourceRef="Event_docsUploaded" targetRef="Event_End_docsUploaded" />
          </bpmn:subProcess>
          <bpmn:serviceTask id="Activity_generateEmail_X0000046" name="generateEmail&#10;X0000046" camunda:type="external" camunda:topic="rekoGenerateDocument">
            <bpmn:documentation>X0000046 - Reko ověřování</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="template">X0000046</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_12tafzi</bpmn:incoming>
            <bpmn:outgoing>Flow_1d7w8pg</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:endEvent id="Event_1lo4h91">
            <bpmn:incoming>Flow_1kz90gz</bpmn:incoming>
          </bpmn:endEvent>
          <bpmn:serviceTask id="Activity_createSblActivity_X0000046" name="Create SBL Activity X0000046" camunda:type="external" camunda:topic="rekoCreateSblActivity">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
              <camunda:inputOutput>
                <camunda:inputParameter name="activityStatus">Done</camunda:inputParameter>
                <camunda:inputParameter name="activityType">Email - Outbound</camunda:inputParameter>
                <camunda:inputParameter name="activityStart">${dateTime().toString()}</camunda:inputParameter>
                <camunda:inputParameter name="activityEnd">${dateTime().plusMinutes(30).toString()}</camunda:inputParameter>
                <camunda:inputParameter name="description">email X0000046 - Reko ověřování</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1d7w8pg</bpmn:incoming>
            <bpmn:outgoing>Flow_1kz90gz</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_1d7w8pg" sourceRef="Activity_generateEmail_X0000046" targetRef="Activity_createSblActivity_X0000046" />
          <bpmn:sequenceFlow id="Flow_1kz90gz" sourceRef="Activity_createSblActivity_X0000046" targetRef="Event_1lo4h91" />
          <bpmn:sequenceFlow id="Flow_12tafzi" sourceRef="Event_after30min" targetRef="Activity_generateEmail_X0000046" />
          <bpmn:boundaryEvent id="Event_after30min" name="30m" cancelActivity="false" attachedToRef="Sub_docsUploaded">
            <bpmn:outgoing>Flow_12tafzi</bpmn:outgoing>
            <bpmn:timerEventDefinition id="TimerEventDefinition_1q1v3q8">
              <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT30M</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
          </bpmn:boundaryEvent>
        </bpmn:subProcess>
        <bpmn:subProcess id="Sub_InitialApplicationCheck" name="Initial application check">
          <bpmn:incoming>Flow_0utqgmy</bpmn:incoming>
          <bpmn:task id="Activity_SetApprovalParameters_1" name="Set Approval Parameters">
            <bpmn:documentation>Set processPhase and processPart</bpmn:documentation>
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:outputParameter name="processPhase">1</camunda:outputParameter>
                <camunda:outputParameter name="processPart">1ST_SCORING</camunda:outputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_toAPPR</bpmn:incoming>
            <bpmn:outgoing>Flow_0zxhpbz</bpmn:outgoing>
          </bpmn:task>
          <bpmn:exclusiveGateway id="Gateway_join2">
            <bpmn:incoming>Flow_FakeFTP</bpmn:incoming>
            <bpmn:incoming>Flow_toJoin2</bpmn:incoming>
            <bpmn:outgoing>Flow_toZDST</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:endEvent id="Event_callAPPR" name="call APPR">
            <bpmn:incoming>Flow_0zxhpbz</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1lhigg5" escalationRef="Escalation_1mm4trv" />
          </bpmn:endEvent>
          <bpmn:sequenceFlow id="Flow_0zxhpbz" sourceRef="Activity_SetApprovalParameters_1" targetRef="Event_callAPPR" />
          <bpmn:serviceTask id="Activity_stateZDPDP" name="state ZDPDP" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">ZDPDP</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_toZDPDP</bpmn:incoming>
            <bpmn:outgoing>Flow_tocheckApplUserRights</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:serviceTask id="Activity_getApplWithIncomes" name="getAppl with incomes" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="isIncome">1</camunda:inputParameter>
                <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
                <camunda:inputParameter name="requestedVariants">REQ</camunda:inputParameter>
                <camunda:inputParameter name="isObligation">0</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_toGetApplWithIncomes</bpmn:incoming>
            <bpmn:outgoing>Flow_toSetAMLQuestShort</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_toSetAMLQuestShort" sourceRef="Activity_getApplWithIncomes" targetRef="Activity_setAMLQuestShort" />
          <bpmn:callActivity id="Activity_solve_applicant" name="solve applicant" calledElement="RekoSolveApplicant">
            <bpmn:extensionElements>
              <camunda:in businessKey="#{execution.processBusinessKey}" />
              <camunda:out source="party" target="party" />
              <camunda:out source="buildingLoanApplication" target="buildingLoanApplication" />
              <camunda:out source="tcherTimestemp" target="tcherTimestemp" />
              <camunda:out source="productId" target="productId" />
              <camunda:in source="applKey" target="applKey" />
              <camunda:in source="busApplId" target="busApplId" />
              <camunda:in source="clientId" target="clientId" />
              <camunda:in source="rstsClientId" target="rstsClientId" />
              <camunda:in source="X-Correlation-Id" target="X-Correlation-Id" />
              <camunda:in source="X-Cl-Context-Id" target="X-Cl-Context-Id" />
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_toSolveApplicant</bpmn:incoming>
            <bpmn:outgoing>Flow_toParallel2</bpmn:outgoing>
          </bpmn:callActivity>
          <bpmn:endEvent id="Activity_CallZDST" name="call ZDST">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_toZDST</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0nyiahl" escalationRef="Escalation_14sedn4" />
          </bpmn:endEvent>
          <bpmn:sequenceFlow id="Flow_toZDST" sourceRef="Gateway_join2" targetRef="Activity_CallZDST" />
          <bpmn:serviceTask id="Activity_checkApplUserRights" name="checkApplUserRights" camunda:type="external" camunda:topic="rekoCheckApplUserRights">
            <bpmn:extensionElements>
              <camunda:properties>
                <camunda:property name="retry_config" value="R5/PT10S,PT60S" />
              </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_toCheckApplUserRights</bpmn:incoming>
            <bpmn:outgoing>Flow_1d9tbls</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:exclusiveGateway id="Gateway_checkApplUserRights" default="SequenceFlow_toGetApplWithIncomes">
            <bpmn:incoming>Flow_tocheckApplUserRights</bpmn:incoming>
            <bpmn:outgoing>Flow_FakeFTP</bpmn:outgoing>
            <bpmn:outgoing>Flow_checkApplUserRightsNOK</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_toGetApplWithIncomes</bpmn:outgoing>
          </bpmn:exclusiveGateway>
          <bpmn:sequenceFlow id="Flow_FakeFTP" name="Fake FTP" sourceRef="Gateway_checkApplUserRights" targetRef="Gateway_join2">
            <bpmn:extensionElements>
              <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
            </bpmn:extensionElements>
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${firstTouchPointFakeClient}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="Flow_checkApplUserRightsNOK" name="check Appl User Rights NOK" sourceRef="Gateway_checkApplUserRights" targetRef="Activity_setApplicationEvent_APPL_IN_UNAUTH_CONCURRENCY">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!firstTouchPointFakeClient &amp;&amp; !checkApplUserRightsResult}</bpmn:conditionExpression>
          </bpmn:sequenceFlow>
          <bpmn:sequenceFlow id="SequenceFlow_toGetApplWithIncomes" sourceRef="Gateway_checkApplUserRights" targetRef="Activity_getApplWithIncomes" />
          <bpmn:sequenceFlow id="Flow_tocheckApplUserRights" sourceRef="Activity_stateZDPDP" targetRef="Gateway_checkApplUserRights" />
          <bpmn:parallelGateway id="Gateway_parallel">
            <bpmn:incoming>Flow_toParallel</bpmn:incoming>
            <bpmn:outgoing>Flow_toStatePZSTAP</bpmn:outgoing>
            <bpmn:outgoing>Flow_toPersonalDataCollected</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_toStatePZSTAP" sourceRef="Gateway_parallel" targetRef="Activity_statePZSTAP" />
          <bpmn:startEvent id="Event_StartEZZA" name="Start EZZA">
            <bpmn:outgoing>Flow_toParallel</bpmn:outgoing>
          </bpmn:startEvent>
          <bpmn:sequenceFlow id="Flow_toParallel" sourceRef="Event_StartEZZA" targetRef="Gateway_parallel" />
          <bpmn:intermediateCatchEvent id="Event_personalDataCollected" name="personalDataCollected">
            <bpmn:incoming>Flow_toPersonalDataCollected</bpmn:incoming>
            <bpmn:outgoing>Flow_0ze8ost</bpmn:outgoing>
            <bpmn:messageEventDefinition id="MessageEventDefinition_03khmwc" messageRef="Message_16p77d8" />
          </bpmn:intermediateCatchEvent>
          <bpmn:sequenceFlow id="Flow_toPersonalDataCollected" sourceRef="Gateway_parallel" targetRef="Event_personalDataCollected" />
          <bpmn:parallelGateway id="Gateway_join">
            <bpmn:incoming>Flow_0ze8ost</bpmn:incoming>
            <bpmn:incoming>Flow_1d9tbls</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_0h2ia4j</bpmn:incoming>
            <bpmn:outgoing>Flow_toZDPDP</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_toZDPDP" sourceRef="Gateway_join" targetRef="Activity_stateZDPDP" />
          <bpmn:sequenceFlow id="Flow_0ze8ost" sourceRef="Event_personalDataCollected" targetRef="Gateway_join" />
          <bpmn:sequenceFlow id="Flow_1d9tbls" sourceRef="Activity_checkApplUserRights" targetRef="Gateway_join" />
          <bpmn:serviceTask id="Activity_statePZSTAP" name="state PZSTAP" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="nextApplicationState">PZSTAP</camunda:inputParameter>
                <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_toStatePZSTAP</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_toSolveApplicant</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="SequenceFlow_toSolveApplicant" sourceRef="Activity_statePZSTAP" targetRef="Activity_solve_applicant" />
          <bpmn:parallelGateway id="Gateway_parallel2">
            <bpmn:incoming>Flow_toParallel2</bpmn:incoming>
            <bpmn:outgoing>Flow_toCheckApplUserRights</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_toCheckFTPCode</bpmn:outgoing>
          </bpmn:parallelGateway>
          <bpmn:sequenceFlow id="Flow_toCheckApplUserRights" sourceRef="Gateway_parallel2" targetRef="Activity_checkApplUserRights" />
          <bpmn:sequenceFlow id="SequenceFlow_toCheckFTPCode" sourceRef="Gateway_parallel2" targetRef="Activity_checkFTPCode" />
          <bpmn:sequenceFlow id="Flow_toParallel2" sourceRef="Activity_solve_applicant" targetRef="Gateway_parallel2" />
          <bpmn:serviceTask id="Activity_setApplicationEvent_APPL_IN_UNAUTH_CONCURRENCY" name="setApplicationEvent (APPL_IN_UNAUTH_CONCURRENCY)" camunda:type="external" camunda:topic="mtgSetApplicationEvent">
            <bpmn:extensionElements>
              <camunda:inputOutput>
                <camunda:inputParameter name="eventType">APPL_IN_UNAUTH_CONCURRENCY</camunda:inputParameter>
                <camunda:inputParameter name="applKey">${applKey}</camunda:inputParameter>
              </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_checkApplUserRightsNOK</bpmn:incoming>
            <bpmn:outgoing>Flow_toJoin2</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_toJoin2" sourceRef="Activity_setApplicationEvent_APPL_IN_UNAUTH_CONCURRENCY" targetRef="Gateway_join2" />
          <bpmn:serviceTask id="Activity_checkFTPCode" name="checkFTPCode" camunda:type="external" camunda:topic="rekoCheckFTPCode">
            <bpmn:incoming>SequenceFlow_toCheckFTPCode</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0h2ia4j</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="SequenceFlow_0h2ia4j" sourceRef="Activity_checkFTPCode" targetRef="Gateway_join" />
          <bpmn:serviceTask id="Activity_setAMLQuestShort" name="setAMLQuest short" camunda:type="external" camunda:topic="rekoSetAmlQuest">
            <bpmn:extensionElements />
            <bpmn:incoming>Flow_toSetAMLQuestShort</bpmn:incoming>
            <bpmn:outgoing>Flow_toAPPR</bpmn:outgoing>
          </bpmn:serviceTask>
          <bpmn:sequenceFlow id="Flow_toAPPR" sourceRef="Activity_setAMLQuestShort" targetRef="Activity_SetApprovalParameters_1" />
        </bpmn:subProcess>
        <bpmn:startEvent id="Event_StartMainProcess">
          <bpmn:outgoing>Flow_0utqgmy</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_0utqgmy" sourceRef="Event_StartMainProcess" targetRef="Sub_InitialApplicationCheck" />
      </bpmn:subProcess>
      <bpmn:subProcess id="Sub_externalPRDN" name="External PRDN" triggeredByEvent="true">
        <bpmn:startEvent id="Event_prdnReceivedMessage" name="prdnReceived" isInterrupting="false">
          <bpmn:outgoing>Flow_0npdn62</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_1h45ehs" messageRef="Message_12dh56i" />
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_0npdn62" sourceRef="Event_prdnReceivedMessage" targetRef="Event_To_Terminate1" />
        <bpmn:endEvent id="Event_To_Terminate1" name="Terminate">
          <bpmn:incoming>Flow_0npdn62</bpmn:incoming>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_1jsi80f" escalationRef="Escalation_29dgsas" />
        </bpmn:endEvent>
      </bpmn:subProcess>
      <bpmn:subProcess id="Sub_externalZDZAM" name="External ZDZA" triggeredByEvent="true">
        <bpmn:endEvent id="Event_0rarv1v" name="ZDZA">
          <bpmn:incoming>Flow_0nlk3ga</bpmn:incoming>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_00hqy5a" escalationRef="Escalation_0sy21dp" />
        </bpmn:endEvent>
        <bpmn:startEvent id="Event_zdzamReceivedMessage" name="zdzamReceived" isInterrupting="false">
          <bpmn:outgoing>Flow_0nlk3ga</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_02sobby" messageRef="Message_257ofnq" />
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_0nlk3ga" sourceRef="Event_zdzamReceivedMessage" targetRef="Event_0rarv1v" />
      </bpmn:subProcess>
      <bpmn:subProcess id="Sub_externalZDST" name="External Storno" triggeredByEvent="true">
        <bpmn:endEvent id="Event_callZDST2" name="ZDST">
          <bpmn:incoming>Flow_1eljc14</bpmn:incoming>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_1r84vn5" escalationRef="Escalation_14sedn4" />
        </bpmn:endEvent>
        <bpmn:startEvent id="Event_stornoReceivedMessage" name="stornoReceived" isInterrupting="false">
          <bpmn:outgoing>Flow_1eljc14</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_04ht9ak" messageRef="Message_283r3o8" />
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_1eljc14" sourceRef="Event_stornoReceivedMessage" targetRef="Event_callZDST2" />
      </bpmn:subProcess>
      <bpmn:subProcess id="Sub_Rejection" name="Rejection (ZDZA)" triggeredByEvent="true">
        <bpmn:startEvent id="Event_startZDZA" name="start ZDZA">
          <bpmn:extensionElements>
            <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
          </bpmn:extensionElements>
          <bpmn:outgoing>Flow_0urzqzx</bpmn:outgoing>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_0fjtjrq" escalationRef="Escalation_0sy21dp" />
        </bpmn:startEvent>
        <bpmn:callActivity id="Activity_Rejection" name="Rejection" calledElement="RekoRejectCancel">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="rejectionState">ZDZAM</camunda:inputParameter>
            </camunda:inputOutput>
            <camunda:in businessKey="#{execution.processBusinessKey}" />
            <camunda:in source="rejectionState" target="rejectionState" />
            <camunda:in source="applKey" target="applKey" />
            <camunda:in source="busApplId" target="busApplId" />
            <camunda:in source="X-Correlation-Id" target="X-Correlation-Id" />
            <camunda:in source="processTp" target="processTp" />
            <camunda:in source="X-Cl-Context-Id" target="X-Cl-Context-Id" />
            <camunda:in source="loanApplicationDealerInfo" target="loanApplicationDealerInfo" />
            <camunda:in source="clientId" target="clientId" />
            <camunda:in source="generatedDocuments" target="generatedDocuments" />
            <camunda:in source="processState" target="processState" />
            <camunda:in source="applicationState" target="applicationState" />
            <camunda:in source="manualApprovalStarted" target="manualApprovalStarted" />
            <camunda:in source="callApprovalResult" target="callApprovalResult" />
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_0urzqzx</bpmn:incoming>
          <bpmn:outgoing>Flow_0ocdd4q</bpmn:outgoing>
        </bpmn:callActivity>
        <bpmn:sequenceFlow id="Flow_0ocdd4q" sourceRef="Activity_Rejection" targetRef="Event_To_Terminate3" />
        <bpmn:sequenceFlow id="Flow_0urzqzx" sourceRef="Event_startZDZA" targetRef="Activity_Rejection" />
        <bpmn:endEvent id="Event_To_Terminate3" name="Terminate">
          <bpmn:extensionElements />
          <bpmn:incoming>Flow_0ocdd4q</bpmn:incoming>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_0y4trpv" escalationRef="Escalation_29dgsas" />
        </bpmn:endEvent>
      </bpmn:subProcess>
      <bpmn:subProcess id="Sub_Cancellation" name="Cancellation (ZDST)" triggeredByEvent="true">
        <bpmn:startEvent id="Event_StartZDST" name="Start ZDST">
          <bpmn:extensionElements>
            <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
          </bpmn:extensionElements>
          <bpmn:outgoing>Flow_020sc6f</bpmn:outgoing>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_0omqsy7" escalationRef="Escalation_14sedn4" />
        </bpmn:startEvent>
        <bpmn:callActivity id="Activity_Cancellation" name="Cancellation" calledElement="RekoRejectCancel">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="rejectionState">STR</camunda:inputParameter>
            </camunda:inputOutput>
            <camunda:in businessKey="#{execution.processBusinessKey}" />
            <camunda:in source="rejectionState" target="rejectionState" />
            <camunda:in source="applKey" target="applKey" />
            <camunda:in source="busApplId" target="busApplId" />
            <camunda:in source="X-Correlation-Id" target="X-Correlation-Id" />
            <camunda:in source="processTp" target="processTp" />
            <camunda:in source="X-Cl-Context-Id" target="X-Cl-Context-Id" />
            <camunda:in source="loanApplicationDealerInfo" target="loanApplicationDealerInfo" />
            <camunda:in source="clientId" target="clientId" />
            <camunda:in source="generatedDocuments" target="generatedDocuments" />
            <camunda:in source="processState" target="processState" />
            <camunda:in source="applicationState" target="applicationState" />
            <camunda:in source="manualApprovalStarted" target="manualApprovalStarted" />
            <camunda:in source="callApprovalResult" target="callApprovalResult" />
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_020sc6f</bpmn:incoming>
          <bpmn:outgoing>Flow_142kqlw</bpmn:outgoing>
        </bpmn:callActivity>
        <bpmn:sequenceFlow id="Flow_142kqlw" sourceRef="Activity_Cancellation" targetRef="Event_To_Terminate2" />
        <bpmn:sequenceFlow id="Flow_020sc6f" sourceRef="Event_StartZDST" targetRef="Activity_Cancellation" />
        <bpmn:endEvent id="Event_To_Terminate2" name="Terminate3">
          <bpmn:extensionElements />
          <bpmn:incoming>Flow_142kqlw</bpmn:incoming>
          <bpmn:escalationEventDefinition id="EscalationEventDefinition_1sr7pp7" escalationRef="Escalation_29dgsas" />
        </bpmn:endEvent>
      </bpmn:subProcess>
      <bpmn:endEvent id="Event_0mivjkd">
        <bpmn:incoming>Flow_0jbybxh</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1l2k4ul" sourceRef="Event_Start" targetRef="Sub_mainProcess" />
      <bpmn:sequenceFlow id="Flow_0jbybxh" sourceRef="Sub_mainProcess" targetRef="Event_0mivjkd" />
      <bpmn:subProcess id="Sub_ContractDocumentSigned" name="ContractDocumentSigned" triggeredByEvent="true">
        <bpmn:startEvent id="Event_contractDocumentSigned" name="contractDocumentSigned" isInterrupting="false">
          <bpmn:outgoing>Flow_toGetApplSigningChannelSel</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_1eu93nh" messageRef="Message_2tdatpo" />
        </bpmn:startEvent>
        <bpmn:endEvent id="Event_ContractDocumentSigned_event_end">
          <bpmn:incoming>Flow_toContractDocumentSigned_event_end</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:exclusiveGateway id="Gateway_is_SMS_or_RBklic" default="Flow_toContractDocumentSigned_neEvent_end">
          <bpmn:incoming>Flow_1hyaie1</bpmn:incoming>
          <bpmn:outgoing>Flow_toClContractSigned_dms</bpmn:outgoing>
          <bpmn:outgoing>Flow_toContractDocumentSigned_neEvent_end</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:endEvent id="Event_ContractDocumentSigned_noEvent_end">
          <bpmn:incoming>Flow_toContractDocumentSigned_neEvent_end</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:serviceTask id="Activity_clContractSigned_dms" name="correlate clContractSigned_dms" camunda:delegateExpression="${correlateMessageDelegate}">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="message">clContractSigned_dms</camunda:inputParameter>
              <camunda:inputParameter name="processInstanceId">${execution.getProcessInstanceId()}</camunda:inputParameter>
              <camunda:inputParameter name="suppressException">1</camunda:inputParameter>
            </camunda:inputOutput>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_toClContractSigned_dms</bpmn:incoming>
          <bpmn:outgoing>Flow_toContractDocumentSigned_event_end</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_signingChannel_SEL" name="get Building Loan Appl - signingChannel (SEL)" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
          <bpmn:documentation>-DTO zmena  loanapplication_primary_owner &gt;&gt; List &lt;prérson&gt; + je to jiny nez NewPersonDTO.
- las-application-v1.yaml

- isIncome = 0 - nenačítáme příjmy
- isObligation = 0 - nanačítáme závazky
- isOwner = 1 - načítáme hlavního žadatele
- requestedVariants = NONE - nenačítáme varianty

vstup:
- PV applKey
- PV busApplId
- PV correlationId
- PV clContextId

výstup ukládáme do PV:
- buildingLoanApplication jako BuildingLoanApplicationDTO
- loanApplicationOwner jako BuildingLoanApplicationOwnerDTO</bpmn:documentation>
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="isIncome">0</camunda:inputParameter>
              <camunda:inputParameter name="isObligation">0</camunda:inputParameter>
              <camunda:inputParameter name="isOwner">0</camunda:inputParameter>
              <camunda:inputParameter name="requestedVariants">SEL</camunda:inputParameter>
              <camunda:inputParameter name="variantSigningChannel">SEL</camunda:inputParameter>
            </camunda:inputOutput>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_toGetApplSigningChannelSel</bpmn:incoming>
          <bpmn:outgoing>Flow_1hyaie1</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_toGetApplSigningChannelSel" sourceRef="Event_contractDocumentSigned" targetRef="Activity_signingChannel_SEL" />
        <bpmn:sequenceFlow id="Flow_toContractDocumentSigned_event_end" sourceRef="Activity_clContractSigned_dms" targetRef="Event_ContractDocumentSigned_event_end" />
        <bpmn:sequenceFlow id="Flow_1hyaie1" sourceRef="Activity_signingChannel_SEL" targetRef="Gateway_is_SMS_or_RBklic" />
        <bpmn:sequenceFlow id="Flow_toClContractSigned_dms" sourceRef="Gateway_is_SMS_or_RBklic" targetRef="Activity_clContractSigned_dms">
          <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(loanApplicationSigningChannel=='15') or (loanApplicationSigningChannel=='80')}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_toContractDocumentSigned_neEvent_end" sourceRef="Gateway_is_SMS_or_RBklic" targetRef="Event_ContractDocumentSigned_noEvent_end" />
      </bpmn:subProcess>
    </bpmn:subProcess>
    <bpmn:boundaryEvent id="Event_Terminate" name="Terminate" attachedToRef="Activity_0ccyx7x">
      <bpmn:outgoing>Flow_1rsmv9d</bpmn:outgoing>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_09bsvol" escalationRef="Escalation_29dgsas" camunda:escalationCodeVariable="Terminate" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1rsmv9d" sourceRef="Event_Terminate" targetRef="Event_1s9gejz" />
    <bpmn:endEvent id="Event_1s9gejz">
      <bpmn:incoming>Flow_1rsmv9d</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_0mkih7z" />
    </bpmn:endEvent>
    <bpmn:startEvent id="Event_1y4ubv5">
      <bpmn:outgoing>Flow_1c1up2e</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1c1up2e" sourceRef="Event_1y4ubv5" targetRef="Activity_0ccyx7x" />
    <bpmn:textAnnotation id="TextAnnotation_1s6k7ad">
      <bpmn:text>also decide if remove insurance if was previously selected</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1m8m0ef" associationDirection="None" sourceRef="Activity_prepareDocumentUpdate" targetRef="TextAnnotation_1s6k7ad" />
  </bpmn:process>
  <bpmn:escalation id="Escalation_1502bkr" name="FINA" escalationCode="FINA" />
  <bpmn:escalation id="Escalation_0sy21dp" name="ZDZA" escalationCode="ZDZA" />
  <bpmn:escalation id="Escalation_0o8w1rz" name="ZDOP" escalationCode="ZDOP" />
  <bpmn:escalation id="Escalation_084gn67" name="MSCH" escalationCode="MSCH" />
  <bpmn:message id="Message_16p77d8" name="personalDataCollected" />
  <bpmn:escalation id="Escalation_14sedn4" name="ZDST" escalationCode="ZDST" />
  <bpmn:escalation id="Escalation_1mm4trv" name="APPR_PRE_CONTR" escalationCode="APPR_PRE_CONTR" />
  <bpmn:message id="Message_2epvd2k" name="manualTaskDone" />
  <bpmn:message id="Message_2nm4rek" name="purposeDeclared" />
  <bpmn:message id="Message_3ofsmu0" name="docsUploaded" />
  <bpmn:message id="Message_3spdfar" name="manualTaskCanceled" />
  <bpmn:message id="Message_1jfelz5" name="applicationFinalized" />
  <bpmn:escalation id="Escalation_1xujcvp" name="DISB" escalationCode="DISB" />
  <bpmn:escalation id="Escalation_1lrr4fe" name="ESAL" escalationCode="ESAL" />
  <bpmn:message id="Message_0vr4ul4" name="clContractSigned_branch" />
  <bpmn:message id="Message_16mguw7" name="contractSignedOnline" />
  <bpmn:message id="Message_37ikigs" name="onbStatusChange" />
  <bpmn:message id="Message_25b4vlo" name="onbChannelChange" />
  <bpmn:escalation id="Escalation_0rkit9g" name="CONTR" escalationCode="CONTR" />
  <bpmn:escalation id="Escalation_18s7skl" name="DOCS" escalationCode="DOCS" />
  <bpmn:message id="Message_12dh56i" name="prdnReceivedMessage" />
  <bpmn:message id="Message_335d402" name="contractDocumentSigned" />
  <bpmn:message id="Message_257ofnq" name="zdzamReceivedMessage" />
  <bpmn:message id="Message_283r3o8" name="stornoReceivedMessage" />
  <bpmn:escalation id="Escalation_29dgsas" name="Terminate" escalationCode="Terminate" />
  <bpmn:message id="Message_2tdatpo" name="contractDocumentSigned" />
  <bpmn:message id="Message_0aqmleu" name="clContractSigned_dms" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="RekoApproval">
      <bpmndi:BPMNShape id="Activity_0ccyx7x_di" bpmnElement="Activity_0ccyx7x" isExpanded="true">
        <dc:Bounds x="210" y="80" width="4790" height="2080" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1oyws25_di" bpmnElement="Event_Start">
        <dc:Bounds x="232" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_08x4pb6_di" bpmnElement="Sub_mainProcess" isExpanded="true">
        <dc:Bounds x="290" y="360" width="4600" height="1570" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1wzcbti_di" bpmnElement="Sub_Finalization" isExpanded="true">
        <dc:Bounds x="390" y="1520" width="1350" height="370" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1q6tec2" bpmnElement="Event_callZDZA2">
        <dc:Bounds x="1652" y="1712" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1646" y="1755" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0roytr7" bpmnElement="Event_callDOCS">
        <dc:Bounds x="1652" y="1792" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1646" y="1835" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bjf3xm_di" bpmnElement="Event_FINA">
        <dc:Bounds x="412" y="1792" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="410" y="1835" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xv8s8c_di" bpmnElement="Activity_SetApprovalParameters_2">
        <dc:Bounds x="1196" y="1770" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rr2cd9" bpmnElement="Activity_stateFNLSCR">
        <dc:Bounds x="1046" y="1770" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_085tec0" bpmnElement="Activity_1y1fn3v">
        <dc:Bounds x="1336" y="1770" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rmlkkh" bpmnElement="Gateway_0zrdfrl" isMarkerVisible="true">
        <dc:Bounds x="1461" y="1785" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d18bn3" bpmnElement="Activity_getApplWithIncomes_finalization">
        <dc:Bounds x="1046" y="1590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0u52viq_di" bpmnElement="Event_EndEmail********">
        <dc:Bounds x="1548" y="1612" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pf1x62_di" bpmnElement="Activity_generateEmail_********">
        <dc:Bounds x="1196" y="1590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0p6owbt_di" bpmnElement="Gateway_0p6owbt" isMarkerVisible="true">
        <dc:Bounds x="1461" y="1605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0eizvnf" bpmnElement="Activity_createSblActivity_********">
        <dc:Bounds x="1336" y="1590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0389ktm_di" bpmnElement="Gateway_isStrabag" isMarkerVisible="true">
        <dc:Bounds x="956" y="1605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0riyl56_di" bpmnElement="Gateway_0riyl56" isMarkerVisible="true">
        <dc:Bounds x="871" y="1605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hs9vj1" bpmnElement="Activity_TriggerEmailNotification" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="610" y="1770" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19iqd1l" bpmnElement="Activity_stateNBDV">
        <dc:Bounds x="480" y="1770" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_05nlfi5" bpmnElement="Sub_applicationFinalized" isExpanded="true">
        <dc:Bounds x="736" y="1750" width="270" height="120" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0m1recw" bpmnElement="Event_Start_applicationFinalized">
        <dc:Bounds x="776" y="1792" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03g1g5d" bpmnElement="Event_End_applicationFinalized">
        <dc:Bounds x="928" y="1792" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18yyqui_di" bpmnElement="Event_applicationFinalized">
        <dc:Bounds x="848" y="1792" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="826" y="1835" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1mcxrwg_di" bpmnElement="Flow_1mcxrwg">
        <di:waypoint x="812" y="1810" />
        <di:waypoint x="848" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03xdlxq_di" bpmnElement="Flow_03xdlxq">
        <di:waypoint x="884" y="1810" />
        <di:waypoint x="928" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_12rkwxd" bpmnElement="Event_after15min">
        <dc:Bounds x="776" y="1732" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="783" y="1775" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0xriihl_di" bpmnElement="Flow_0xriihl">
        <di:waypoint x="580" y="1810" />
        <di:waypoint x="610" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ijninc_di" bpmnElement="Flow_1ijninc">
        <di:waypoint x="448" y="1810" />
        <di:waypoint x="480" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ssl10b_di" bpmnElement="Flow_docGenApproval">
        <di:waypoint x="1511" y="1810" />
        <di:waypoint x="1652" y="1810" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1568" y="1793" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mjevl_di" bpmnElement="Flow_declined2">
        <di:waypoint x="1486" y="1785" />
        <di:waypoint x="1486" y="1730" />
        <di:waypoint x="1652" y="1730" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1576" y="1713" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uurkko_di" bpmnElement="Flow_1uurkko">
        <di:waypoint x="710" y="1810" />
        <di:waypoint x="736" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e5uw69_di" bpmnElement="Flow_0e5uw69">
        <di:waypoint x="1146" y="1810" />
        <di:waypoint x="1196" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1slf1ue_di" bpmnElement="Flow_1slf1ue">
        <di:waypoint x="1296" y="1810" />
        <di:waypoint x="1336" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eealr6_di" bpmnElement="Flow_0eealr6">
        <di:waypoint x="1006" y="1810" />
        <di:waypoint x="1046" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gk5v5s_di" bpmnElement="Flow_1gk5v5s">
        <di:waypoint x="1436" y="1810" />
        <di:waypoint x="1461" y="1810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pjfsjy_di" bpmnElement="Flow_isNotStrabag">
        <di:waypoint x="1006" y="1630" />
        <di:waypoint x="1046" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z0wdl9_di" bpmnElement="Flow_0z0wdl9">
        <di:waypoint x="1146" y="1630" />
        <di:waypoint x="1196" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rbii3j_di" bpmnElement="Flow_0rbii3j">
        <di:waypoint x="1511" y="1630" />
        <di:waypoint x="1548" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w7ren3_di" bpmnElement="Flow_1w7ren3">
        <di:waypoint x="1296" y="1630" />
        <di:waypoint x="1336" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y1jpk6_di" bpmnElement="Flow_0y1jpk6">
        <di:waypoint x="1436" y="1630" />
        <di:waypoint x="1461" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14rku73_di" bpmnElement="Flow_isStrabag">
        <di:waypoint x="981" y="1605" />
        <di:waypoint x="981" y="1565" />
        <di:waypoint x="1486" y="1565" />
        <di:waypoint x="1486" y="1605" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1twyn0y_di" bpmnElement="Flow_1ykv3nv">
        <di:waypoint x="896" y="1655" />
        <di:waypoint x="896" y="1695" />
        <di:waypoint x="1486" y="1695" />
        <di:waypoint x="1486" y="1655" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="905" y="1590" width="51" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rncl04_di" bpmnElement="Flow_1rncl04">
        <di:waypoint x="921" y="1630" />
        <di:waypoint x="956" y="1630" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="893" y="1658" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0h6m4i0_di" bpmnElement="Flow_0h6m4i0">
        <di:waypoint x="794" y="1732" />
        <di:waypoint x="794" y="1630" />
        <di:waypoint x="871" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_07xys3p" bpmnElement="Sub_disbursement" isExpanded="true">
        <dc:Bounds x="1780" y="1450" width="1220" height="390" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03ecurf_di" bpmnElement="Activity_assignCategory">
        <dc:Bounds x="2420" y="1500" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1brtozp" bpmnElement="Gateway_1dygd37">
        <dc:Bounds x="2555" y="1630" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cngm3x" bpmnElement="Gateway_0qd5rkz">
        <dc:Bounds x="2185" y="1630" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_003g1lc" bpmnElement="Activity_createSblActivity">
        <dc:Bounds x="2420" y="1730" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ceu10d" bpmnElement="Activity_generateDocumentX0048">
        <dc:Bounds x="2280" y="1730" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vf4owk" bpmnElement="Activity_generateDocumentP1541">
        <dc:Bounds x="2280" y="1615" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gk8wy9" bpmnElement="Activity_updateOpportunityDisb">
        <dc:Bounds x="2280" y="1500" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ula1nx" bpmnElement="Activity_stateSMLAKCdisbursement">
        <dc:Bounds x="2040" y="1615" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_193hkk3" bpmnElement="Activity_getApplWithInc">
        <dc:Bounds x="1900" y="1615" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1xq683f" bpmnElement="Event_End_Disbursement">
        <dc:Bounds x="2922" y="1637" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2910" y="1683" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06ih6vj" bpmnElement="Event_DISB">
        <dc:Bounds x="1822" y="1637" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1829" y="1680" width="27" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1i9m18m" bpmnElement="Activity_cibis_createProduct">
        <dc:Bounds x="2780" y="1615" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10p0ps9_di" bpmnElement="Activity_transferDocs">
        <dc:Bounds x="2640" y="1615" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0da49bu" bpmnElement="Activity_setDocumentSmStateP0001541">
        <dc:Bounds x="2420" y="1615" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0e4h8eo_di" bpmnElement="Flow_0e4h8eo">
        <di:waypoint x="2520" y="1540" />
        <di:waypoint x="2580" y="1540" />
        <di:waypoint x="2580" y="1630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_197zmhs_di" bpmnElement="Flow_197zmhs">
        <di:waypoint x="2605" y="1655" />
        <di:waypoint x="2640" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t1xllm_di" bpmnElement="Flow_1t1xllm">
        <di:waypoint x="2210" y="1680" />
        <di:waypoint x="2210" y="1770" />
        <di:waypoint x="2280" y="1770" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9gjax_di" bpmnElement="Flow_1k9gjax">
        <di:waypoint x="2235" y="1655" />
        <di:waypoint x="2280" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rmmzcj_di" bpmnElement="Flow_0rmmzcj">
        <di:waypoint x="2210" y="1630" />
        <di:waypoint x="2210" y="1540" />
        <di:waypoint x="2280" y="1540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_139ic3e_di" bpmnElement="Flow_139ic3e">
        <di:waypoint x="2740" y="1655" />
        <di:waypoint x="2780" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wklmsi_di" bpmnElement="Flow_1wklmsi">
        <di:waypoint x="2520" y="1770" />
        <di:waypoint x="2580" y="1770" />
        <di:waypoint x="2580" y="1680" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1weia8x_di" bpmnElement="Flow_1weia8x">
        <di:waypoint x="2380" y="1770" />
        <di:waypoint x="2420" y="1770" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16oyx34_di" bpmnElement="Flow_16oyx34">
        <di:waypoint x="2380" y="1655" />
        <di:waypoint x="2420" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lpxog_di" bpmnElement="Flow_03lpxog">
        <di:waypoint x="2380" y="1540" />
        <di:waypoint x="2420" y="1540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xhu69m_di" bpmnElement="Flow_1xhu69m">
        <di:waypoint x="2140" y="1655" />
        <di:waypoint x="2185" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u97u7p_di" bpmnElement="Flow_0u97u7p">
        <di:waypoint x="1858" y="1655" />
        <di:waypoint x="1900" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0myerzk_di" bpmnElement="Flow_0myerzk">
        <di:waypoint x="2880" y="1655" />
        <di:waypoint x="2922" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dqlu21_di" bpmnElement="Flow_0dqlu21">
        <di:waypoint x="2000" y="1655" />
        <di:waypoint x="2040" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o8helq_di" bpmnElement="Flow_0o8helq">
        <di:waypoint x="2520" y="1655" />
        <di:waypoint x="2555" y="1655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1kpx5la_di" bpmnElement="Sub_DocGeneration" isExpanded="true">
        <dc:Bounds x="1780" y="400" width="3070" height="1010" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hrz014" bpmnElement="Activity_calculateBuildingLoanIPS">
        <dc:Bounds x="2360" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0h9dkvm" bpmnElement="Activity_stateSMLPOD">
        <dc:Bounds x="4390" y="609" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ejqhjx" bpmnElement="Activity_stateSMLG">
        <dc:Bounds x="3310" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15o6ul6" bpmnElement="Activity_getAppl">
        <dc:Bounds x="2120" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qjxgt9" bpmnElement="Activity_stateZDSCH">
        <dc:Bounds x="1900" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ud8hb4" bpmnElement="Event_onbStatusChange">
        <dc:Bounds x="3542" y="632" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3517" y="675" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04hmlub" bpmnElement="Activity_setSigningChannel_81">
        <dc:Bounds x="3790" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d6zbjh" bpmnElement="Activity_ReGenerateDocument_P0001540">
        <dc:Bounds x="3090" y="1030" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pw9syp" bpmnElement="Activity_ReGenerateDocument_P0001543">
        <dc:Bounds x="2950" y="1030" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06xf3lx" bpmnElement="Activity_GenerateDocument_P0001540">
        <dc:Bounds x="3030" y="810" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ereqi6_di" bpmnElement="Activity_setSigningChannel_10">
        <dc:Bounds x="3790" y="500" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12jzxtm_di" bpmnElement="Gateway_join3" isMarkerVisible="true">
        <dc:Bounds x="4145" y="625" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uhqpl7_di" bpmnElement="Event_clContractSigned_branch">
        <dc:Bounds x="3542" y="522" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3520" y="565" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1mmyysc_di" bpmnElement="Sub_clientApproval" isExpanded="true">
        <dc:Bounds x="3680" y="1029" width="320" height="122" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fbzxkr" bpmnElement="Activity_addClientApproval">
        <dc:Bounds x="3790" y="1049" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1css0tu_di" bpmnElement="Event_End_clientApproval">
        <dc:Bounds x="3932" y="1071" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0i9egdj_di" bpmnElement="Event_Start_clientApproval">
        <dc:Bounds x="3712" y="1071" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0mzledh_di" bpmnElement="Flow_0mzledh">
        <di:waypoint x="3890" y="1089" />
        <di:waypoint x="3932" y="1089" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_050paj9_di" bpmnElement="Flow_050paj9">
        <di:waypoint x="3748" y="1089" />
        <di:waypoint x="3790" y="1089" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_02g8ung_di" bpmnElement="Activity_setContractSigned">
        <dc:Bounds x="4250" y="610" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rovn9t_di" bpmnElement="Activity_GenerateDocument_P0001543">
        <dc:Bounds x="2760" y="810" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1nnk95j_di" bpmnElement="Event_10t4fmz">
        <dc:Bounds x="4772" y="1302" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4768" y="1345" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1k4ynuj_di" bpmnElement="Gateway_docGenerationJoin" isMarkerVisible="true">
        <dc:Bounds x="2045" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01oovk7_di" bpmnElement="Event_contractSignedOnline">
        <dc:Bounds x="3542" y="1072" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3555" y="1056" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lkhz42_di" bpmnElement="Gateway_waitForEvent">
        <dc:Bounds x="3425" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05dougv_di" bpmnElement="Event_applicationFinalized2">
        <dc:Bounds x="3432" y="1072" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3336" y="1076" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_188aknn_di" bpmnElement="Gateway_approvalResult" isMarkerVisible="true">
        <dc:Bounds x="3425" y="1295" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3437" y="1355" width="28" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04be8ja_di" bpmnElement="Gateway_1stRound" isMarkerVisible="true">
        <dc:Bounds x="2615" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a4tj2v_di" bpmnElement="Gateway_docGenerationJoin3" isMarkerVisible="true">
        <dc:Bounds x="3245" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1xhuc32_di" bpmnElement="Event_DOCS">
        <dc:Bounds x="1822" y="962" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1814" y="1005" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wmejdj_di" bpmnElement="Activity_getDealerInfo">
        <dc:Bounds x="2240" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1rsoij5_di" bpmnElement="Activity_getSBLPartyDetail">
        <dc:Bounds x="2490" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ffgv3h_di" bpmnElement="Activity_callApproval_1st_scoring_only">
        <dc:Bounds x="3400" y="1160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03e6h6m" bpmnElement="Activity_callBuildingLoanApproval_2">
        <dc:Bounds x="4540" y="609" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yw1ozq" bpmnElement="Gateway_0t5ksiv" isMarkerVisible="true">
        <dc:Bounds x="4675" y="624" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0z707uh" bpmnElement="Event_callDISB2">
        <dc:Bounds x="4772" y="631" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4768" y="674" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0p2uovu" bpmnElement="Event_callZDZA3">
        <dc:Bounds x="4772" y="553" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4766" y="596" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c6za39" bpmnElement="Event_clContractSigned_dms">
        <dc:Bounds x="3542" y="432" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3523" y="475" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_192486p_di" bpmnElement="Gateway_192486p" isMarkerVisible="true">
        <dc:Bounds x="3675" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qdbtwa_di" bpmnElement="Activity_update_signChannel">
        <dc:Bounds x="4000" y="500" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ozmehj" bpmnElement="Activity_signing_channel_15">
        <dc:Bounds x="4120" y="1050" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10it41a" bpmnElement="Event_onbChannelChange">
        <dc:Bounds x="3542" y="862" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3519" y="905" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1canh6l_di" bpmnElement="Activity_updateChannel">
        <dc:Bounds x="3790" y="840" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03k5116_di" bpmnElement="Gateway_03k5116" isMarkerVisible="true">
        <dc:Bounds x="3675" y="625" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3623" y="600" width="54" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_184jewl" bpmnElement="Activity_channel10">
        <dc:Bounds x="3790" y="710" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1r6aq9o_di" bpmnElement="Gateway_join_9o" isMarkerVisible="true">
        <dc:Bounds x="4025" y="855" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00tjc7e" bpmnElement="Activity_update_signChannel2">
        <dc:Bounds x="4000" y="940" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1t728ng_di" bpmnElement="Activity_prepareDocumentUpdate">
        <dc:Bounds x="2680" y="1030" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1uceax0" bpmnElement="Activity_setDocumentSmStateP0001543">
        <dc:Bounds x="2900" y="810" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ighiye" bpmnElement="Activity_generate_P0001644_1">
        <dc:Bounds x="2900" y="940" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15yokwg" bpmnElement="Activity_Generate_P0001644_2">
        <dc:Bounds x="2950" y="1120" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lz6q6l" bpmnElement="Activity_delete_P0001644">
        <dc:Bounds x="2950" y="1220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wp9rz7" bpmnElement="Gateway_insuranceConfirmed" isMarkerVisible="true">
        <dc:Bounds x="2865" y="1185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06chx7d" bpmnElement="Gateway_join_insuranceConfirmed" isMarkerVisible="true">
        <dc:Bounds x="3115" y="1185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mvezd7_di" bpmnElement="Gateway_parallel_1">
        <dc:Bounds x="2675" y="825" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s9yoju" bpmnElement="Gateway_join_parallel_1">
        <dc:Bounds x="3175" y="825" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0v1pwnb" bpmnElement="Gateway_parallel_2">
        <dc:Bounds x="2815" y="1045" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0khxrmt" bpmnElement="Gateway_join_parallel_2">
        <dc:Bounds x="3245" y="1045" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xjdkmf" bpmnElement="Gateway_selectedInsurance" isMarkerVisible="true">
        <dc:Bounds x="2765" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07bp6r3" bpmnElement="Gateway_join_selectedInsurance" isMarkerVisible="true">
        <dc:Bounds x="3055" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_07tc4s5_di" bpmnElement="Flow_07tc4s5">
        <di:waypoint x="2000" y="980" />
        <di:waypoint x="2045" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0rkadzo" bpmnElement="Flow_0gw7e9s">
        <di:waypoint x="3578" y="650" />
        <di:waypoint x="3675" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13e7ck9_di" bpmnElement="Flow_13e7ck9">
        <di:waypoint x="3050" y="1070" />
        <di:waypoint x="3090" y="1070" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_193am9z_di" bpmnElement="Flow_193am9z">
        <di:waypoint x="2460" y="980" />
        <di:waypoint x="2490" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ivoyq8_di" bpmnElement="Flow_0ivoyq8">
        <di:waypoint x="4195" y="650" />
        <di:waypoint x="4250" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t2ixmt_di" bpmnElement="Flow_0t2ixmt">
        <di:waypoint x="3890" y="650" />
        <di:waypoint x="4145" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aq4xth_di" bpmnElement="Flow_1aq4xth">
        <di:waypoint x="3890" y="540" />
        <di:waypoint x="4000" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04grr9t_di" bpmnElement="Flow_04grr9t">
        <di:waypoint x="3578" y="540" />
        <di:waypoint x="3675" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a9th1e_di" bpmnElement="Flow_0a9th1e">
        <di:waypoint x="4000" y="1090" />
        <di:waypoint x="4120" y="1090" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m6javp_di" bpmnElement="Flow_1m6javp">
        <di:waypoint x="4350" y="650" />
        <di:waypoint x="4390" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p1cg95_di" bpmnElement="Flow_0p1cg95">
        <di:waypoint x="2095" y="980" />
        <di:waypoint x="2120" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0livisq_di" bpmnElement="Flow_0livisq">
        <di:waypoint x="3578" y="1090" />
        <di:waypoint x="3680" y="1090" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r7mw3f_di" bpmnElement="Flow_0r7mw3f">
        <di:waypoint x="3452" y="957" />
        <di:waypoint x="3480" y="650" />
        <di:waypoint x="3542" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kt9xg4_di" bpmnElement="Flow_1kt9xg4">
        <di:waypoint x="3452" y="957" />
        <di:waypoint x="3480" y="540" />
        <di:waypoint x="3542" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09sbjzx_di" bpmnElement="Flow_09sbjzx">
        <di:waypoint x="3463" y="993" />
        <di:waypoint x="3547" y="1077" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wiheae_di" bpmnElement="Flow_1wiheae">
        <di:waypoint x="3410" y="980" />
        <di:waypoint x="3425" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1laad81_di" bpmnElement="Flow_1laad81">
        <di:waypoint x="3450" y="1005" />
        <di:waypoint x="3450" y="1072" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_030ajgk_di" bpmnElement="Flow_030ajgk">
        <di:waypoint x="3475" y="1320" />
        <di:waypoint x="4772" y="1320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3553" y="1293" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09et9k2_di" bpmnElement="Flow_09et9k2">
        <di:waypoint x="3450" y="1108" />
        <di:waypoint x="3450" y="1160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gsqyg2_di" bpmnElement="Flow_0gsqyg2">
        <di:waypoint x="2640" y="1005" />
        <di:waypoint x="2640" y="1070" />
        <di:waypoint x="2680" y="1070" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2597" y="1076" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wdu5k4_di" bpmnElement="Flow_1wdu5k4">
        <di:waypoint x="2640" y="955" />
        <di:waypoint x="2640" y="850" />
        <di:waypoint x="2675" y="850" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2570" y="886" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rk0aow_di" bpmnElement="Flow_1rk0aow">
        <di:waypoint x="3295" y="980" />
        <di:waypoint x="3310" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01hrxq5_di" bpmnElement="Flow_01hrxq5">
        <di:waypoint x="2860" y="850" />
        <di:waypoint x="2900" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yixis5_di" bpmnElement="Flow_0yixis5">
        <di:waypoint x="2220" y="980" />
        <di:waypoint x="2240" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a2pgzt_di" bpmnElement="Flow_1a2pgzt">
        <di:waypoint x="1858" y="980" />
        <di:waypoint x="1900" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0im42v1_di" bpmnElement="Flow_0im42v1">
        <di:waypoint x="2340" y="980" />
        <di:waypoint x="2360" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rdllvi_di" bpmnElement="Flow_0rdllvi">
        <di:waypoint x="2590" y="980" />
        <di:waypoint x="2615" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l370yf_di" bpmnElement="Flow_0l370yf">
        <di:waypoint x="3450" y="1240" />
        <di:waypoint x="3450" y="1295" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05axo4f_di" bpmnElement="Flow_1rp3vkn">
        <di:waypoint x="3425" y="1320" />
        <di:waypoint x="2070" y="1320" />
        <di:waypoint x="2070" y="1005" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3227" y="1293" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fcu878_di" bpmnElement="Flow_1fcu878">
        <di:waypoint x="4490" y="649" />
        <di:waypoint x="4540" y="649" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_19cnkdv" bpmnElement="Event_callDISB">
        <di:waypoint x="4725" y="649" />
        <di:waypoint x="4772" y="649" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4690" y="693" width="83" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l4mqoq_di" bpmnElement="Flow_1l4mqoq">
        <di:waypoint x="4640" y="649" />
        <di:waypoint x="4675" y="649" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p2l379_di" bpmnElement="Flow_declined3">
        <di:waypoint x="4700" y="624" />
        <di:waypoint x="4700" y="571" />
        <di:waypoint x="4772" y="571" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4709" y="554" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xuc1tz_di" bpmnElement="Flow_0xuc1tz">
        <di:waypoint x="3130" y="850" />
        <di:waypoint x="3175" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j4wjzq_di" bpmnElement="Flow_1j4wjzq">
        <di:waypoint x="3190" y="1070" />
        <di:waypoint x="3245" y="1070" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jfh5d2_di" bpmnElement="Flow_1jfh5d2">
        <di:waypoint x="3725" y="540" />
        <di:waypoint x="3790" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k4w8ni_di" bpmnElement="Flow_0k4w8ni">
        <di:waypoint x="3450" y="955" />
        <di:waypoint x="3450" y="450" />
        <di:waypoint x="3542" y="450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xq70od_di" bpmnElement="Flow_1xq70od">
        <di:waypoint x="3578" y="450" />
        <di:waypoint x="3700" y="450" />
        <di:waypoint x="3700" y="515" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gb9jck_di" bpmnElement="Flow_0gb9jck">
        <di:waypoint x="4100" y="540" />
        <di:waypoint x="4170" y="540" />
        <di:waypoint x="4170" y="625" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ov9wku_di" bpmnElement="Flow_0ov9wku">
        <di:waypoint x="4170" y="1050" />
        <di:waypoint x="4170" y="675" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dxi3t3_di" bpmnElement="Flow_1dxi3t3">
        <di:waypoint x="3458" y="963" />
        <di:waypoint x="3500" y="880" />
        <di:waypoint x="3542" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ctow30_di" bpmnElement="Flow_0ctow30">
        <di:waypoint x="3578" y="880" />
        <di:waypoint x="3790" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s82eai_di" bpmnElement="Flow_FINISHED">
        <di:waypoint x="3725" y="650" />
        <di:waypoint x="3790" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3696" y="603" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bu8jll_di" bpmnElement="Flow_REJECTED_CANCELED">
        <di:waypoint x="3700" y="675" />
        <di:waypoint x="3700" y="750" />
        <di:waypoint x="3790" y="750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3713" y="700" width="73" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fkb6wr_di" bpmnElement="Flow_1fkb6wr">
        <di:waypoint x="3890" y="750" />
        <di:waypoint x="4050" y="750" />
        <di:waypoint x="4050" y="855" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04mqc5y_di" bpmnElement="Flow_04mqc5y">
        <di:waypoint x="3890" y="880" />
        <di:waypoint x="4025" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o7smm3_di" bpmnElement="Flow_1o7smm3">
        <di:waypoint x="4050" y="905" />
        <di:waypoint x="4050" y="940" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u5rgte_di" bpmnElement="Flow_1u5rgte">
        <di:waypoint x="4000" y="980" />
        <di:waypoint x="3475" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dkc8ux_di" bpmnElement="Flow_0dkc8ux">
        <di:waypoint x="2780" y="1070" />
        <di:waypoint x="2815" y="1070" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13hckw7_di" bpmnElement="Flow_13hckw7">
        <di:waypoint x="3000" y="850" />
        <di:waypoint x="3030" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04gveby_di" bpmnElement="Flow_04gveby">
        <di:waypoint x="2725" y="850" />
        <di:waypoint x="2760" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c7ne1n_di" bpmnElement="Flow_fghabj8">
        <di:waypoint x="2700" y="875" />
        <di:waypoint x="2700" y="980" />
        <di:waypoint x="2765" y="980" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2717" y="936" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y60ai6_di" bpmnElement="Flow_0y60ai6">
        <di:waypoint x="3225" y="850" />
        <di:waypoint x="3270" y="850" />
        <di:waypoint x="3270" y="955" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hviacv_di" bpmnElement="Flow_1hviacv">
        <di:waypoint x="3000" y="980" />
        <di:waypoint x="3055" y="980" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qwopln_di" bpmnElement="Flow_0qwopln">
        <di:waypoint x="2865" y="1070" />
        <di:waypoint x="2950" y="1070" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04iux85_di" bpmnElement="Flow_insurance_confirmed">
        <di:waypoint x="2890" y="1185" />
        <di:waypoint x="2890" y="1160" />
        <di:waypoint x="2950" y="1160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2885" y="1126" width="49" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1in7567_di" bpmnElement="Flow_insurance_removed">
        <di:waypoint x="2890" y="1235" />
        <di:waypoint x="2890" y="1260" />
        <di:waypoint x="2950" y="1260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2886" y="1266" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0srfs28_di" bpmnElement="Flow_0srfs28">
        <di:waypoint x="2840" y="1095" />
        <di:waypoint x="2840" y="1210" />
        <di:waypoint x="2865" y="1210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03uzsso_di" bpmnElement="Flow_03uzsso">
        <di:waypoint x="3270" y="1045" />
        <di:waypoint x="3270" y="1005" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00a1jo4_di" bpmnElement="Flow_00a1jo4">
        <di:waypoint x="3165" y="1210" />
        <di:waypoint x="3270" y="1210" />
        <di:waypoint x="3270" y="1095" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12qw6ei_di" bpmnElement="Flow_12qw6ei">
        <di:waypoint x="3050" y="1160" />
        <di:waypoint x="3140" y="1160" />
        <di:waypoint x="3140" y="1185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_129deap_di" bpmnElement="Flow_129deap">
        <di:waypoint x="3050" y="1260" />
        <di:waypoint x="3140" y="1260" />
        <di:waypoint x="3140" y="1235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k14tyi_di" bpmnElement="Flow_1k14tyi">
        <di:waypoint x="2815" y="980" />
        <di:waypoint x="2900" y="980" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2822" y="946" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12hjv42_di" bpmnElement="Flow_12hjv42">
        <di:waypoint x="3105" y="980" />
        <di:waypoint x="3200" y="980" />
        <di:waypoint x="3200" y="875" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r3z0ow_di" bpmnElement="Flow_0r3z0ow">
        <di:waypoint x="2790" y="955" />
        <di:waypoint x="2790" y="910" />
        <di:waypoint x="3080" y="910" />
        <di:waypoint x="3080" y="955" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_18r6kh1_di" bpmnElement="Sub_Approval" isExpanded="true">
        <dc:Bounds x="390" y="850" width="1350" height="630" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0koubxb" bpmnElement="Event_callZDZA">
        <dc:Bounds x="1662" y="1342" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1656" y="1385" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1an15ml" bpmnElement="Event_callFINA">
        <dc:Bounds x="1662" y="1412" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1659" y="1455" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jaon14" bpmnElement="Event_callZDST">
        <dc:Bounds x="1662" y="1270" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1656" y="1313" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0d9ybak_di" bpmnElement="Activity_CallBuildingLoanApproval">
        <dc:Bounds x="520" y="1168" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1lospgx_di" bpmnElement="Gateway_1lospgx" isMarkerVisible="true">
        <dc:Bounds x="645" y="1183" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05xe6gs_di" bpmnElement="Gateway_waitForEvent2">
        <dc:Bounds x="1085" y="1183" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00v3lmx" bpmnElement="Activity_createManualTask">
        <dc:Bounds x="930" y="1168" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1j1g4nm" bpmnElement="Activity_StateMSCH1">
        <dc:Bounds x="800" y="1168" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1of0clx" bpmnElement="Activity_state_ZDDEKL">
        <dc:Bounds x="800" y="1060" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1s0xwba" bpmnElement="Activity_state_ZDDOKL">
        <dc:Bounds x="1060" y="1060" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18ffo9g_di" bpmnElement="Event_APPR">
        <dc:Bounds x="430" y="1190" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="435" y="1233" width="31" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13muwv4" bpmnElement="Gateway_16mgwhc" isMarkerVisible="true">
        <dc:Bounds x="1655" y="1075" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04hzsgt" bpmnElement="Event_manualTaskDone" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1242" y="1190" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1218" y="1231" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0u0f4rz" bpmnElement="Event_purposeDeclared" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="962" y="1082" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="1123" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nb7q09" bpmnElement="Event_manualTaskCanceled" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1242" y="1270" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1218" y="1313" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bjky9u" bpmnElement="Activity_state_ZDPDP">
        <dc:Bounds x="800" y="870" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pmol22_di" bpmnElement="Sub_docsUploaded" isExpanded="true">
        <dc:Bounds x="1200" y="1040" width="270" height="120" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0g3sgk2_di" bpmnElement="Event_Start_docsUploaded">
        <dc:Bounds x="1240" y="1082" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1g8q8e9" bpmnElement="Event_docsUploaded" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1312" y="1082" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1295" y="1123" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1pyxyli_di" bpmnElement="Event_End_docsUploaded">
        <dc:Bounds x="1392" y="1082" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_188z0ss_di" bpmnElement="Flow_188z0ss">
        <di:waypoint x="1276" y="1100" />
        <di:waypoint x="1312" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ec1tje_di" bpmnElement="Flow_0ec1tje">
        <di:waypoint x="1348" y="1100" />
        <di:waypoint x="1392" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1kdhu06" bpmnElement="Activity_generateEmail_X0000046" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1310" y="930" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lo4h91_di" bpmnElement="Event_1lo4h91">
        <dc:Bounds x="1572" y="952" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wqjhta" bpmnElement="Activity_createSblActivity_X0000046">
        <dc:Bounds x="1440" y="930" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1sqmlmc_di" bpmnElement="Event_after30min">
        <dc:Bounds x="1240" y="1022" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1247" y="1065" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0l57692_di" bpmnElement="Flow_declined">
        <di:waypoint x="683" y="1220" />
        <di:waypoint x="840" y="1360" />
        <di:waypoint x="1662" y="1360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="859" y="1343" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_037e09s_di" bpmnElement="Flow_approved">
        <di:waypoint x="678" y="1225" />
        <di:waypoint x="780" y="1430" />
        <di:waypoint x="1662" y="1430" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="857" y="1413" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l4dxtx_di" bpmnElement="Flow_0l4dxtx">
        <di:waypoint x="620" y="1208" />
        <di:waypoint x="645" y="1208" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rijd0k_di" bpmnElement="Flow_0rijd0k">
        <di:waypoint x="695" y="1208" />
        <di:waypoint x="800" y="1208" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="707" y="1174" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10iym5i_di" bpmnElement="Flow_10iym5i">
        <di:waypoint x="1030" y="1208" />
        <di:waypoint x="1085" y="1208" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yze336_di" bpmnElement="Flow_0yze336">
        <di:waypoint x="900" y="1208" />
        <di:waypoint x="930" y="1208" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_043w1ws_di" bpmnElement="Flow_043w1ws">
        <di:waypoint x="670" y="1183" />
        <di:waypoint x="670" y="1100" />
        <di:waypoint x="800" y="1100" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="690" y="1066" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ka6vwr_di" bpmnElement="Flow_0ka6vwr">
        <di:waypoint x="900" y="1100" />
        <di:waypoint x="962" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h7e9kg_di" bpmnElement="Flow_1h7e9kg">
        <di:waypoint x="466" y="1208" />
        <di:waypoint x="520" y="1208" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04kphzc_di" bpmnElement="Flow_04kphzc">
        <di:waypoint x="1680" y="1075" />
        <di:waypoint x="1680" y="910" />
        <di:waypoint x="900" y="910" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ubvlxr_di" bpmnElement="Flow_1ubvlxr">
        <di:waypoint x="1160" y="1100" />
        <di:waypoint x="1200" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wy61x9_di" bpmnElement="Flow_0wy61x9">
        <di:waypoint x="1278" y="1208" />
        <di:waypoint x="1680" y="1208" />
        <di:waypoint x="1680" y="1125" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mhdtya_di" bpmnElement="Flow_1mhdtya">
        <di:waypoint x="1135" y="1208" />
        <di:waypoint x="1242" y="1208" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u3ysbp_di" bpmnElement="Flow_1u3ysbp">
        <di:waypoint x="998" y="1100" />
        <di:waypoint x="1060" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cjp8mr_di" bpmnElement="Flow_1cjp8mr">
        <di:waypoint x="1278" y="1288" />
        <di:waypoint x="1662" y="1288" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wxfl2o_di" bpmnElement="Flow_0wxfl2o">
        <di:waypoint x="1110" y="1233" />
        <di:waypoint x="1110" y="1288" />
        <di:waypoint x="1242" y="1288" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cl9dir_di" bpmnElement="Flow_0cl9dir">
        <di:waypoint x="1470" y="1100" />
        <di:waypoint x="1655" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pp85dv_di" bpmnElement="Flow_0pp85dv">
        <di:waypoint x="800" y="910" />
        <di:waypoint x="570" y="910" />
        <di:waypoint x="570" y="1168" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d7w8pg_di" bpmnElement="Flow_1d7w8pg">
        <di:waypoint x="1410" y="970" />
        <di:waypoint x="1440" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kz90gz_di" bpmnElement="Flow_1kz90gz">
        <di:waypoint x="1540" y="970" />
        <di:waypoint x="1572" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12tafzi_di" bpmnElement="Flow_12tafzi">
        <di:waypoint x="1258" y="1022" />
        <di:waypoint x="1258" y="970" />
        <di:waypoint x="1310" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0un82y6_di" bpmnElement="Sub_InitialApplicationCheck" isExpanded="true">
        <dc:Bounds x="390" y="400" width="1350" height="410" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0x9x0l6" bpmnElement="Activity_SetApprovalParameters_1">
        <dc:Bounds x="1530" y="454" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05qf2f0_di" bpmnElement="Gateway_join2" isMarkerVisible="true">
        <dc:Bounds x="1495" y="594" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tdb78f_di" bpmnElement="Event_callAPPR">
        <dc:Bounds x="1662" y="476" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1658" y="519" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cbs6c6_di" bpmnElement="Activity_stateZDPDP">
        <dc:Bounds x="1150" y="579" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pc586t" bpmnElement="Activity_getApplWithIncomes">
        <dc:Bounds x="1270" y="454" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xi7fq8_di" bpmnElement="Activity_solve_applicant">
        <dc:Bounds x="670" y="579" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1aq5wqg_di" bpmnElement="Activity_CallZDST" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1662" y="601" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1657" y="644" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0m0poh4_di" bpmnElement="Activity_checkApplUserRights">
        <dc:Bounds x="910" y="700" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mca6ou_di" bpmnElement="Gateway_checkApplUserRights" isMarkerVisible="true">
        <dc:Bounds x="1295" y="594" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02g2tt1_di" bpmnElement="Gateway_parallel">
        <dc:Bounds x="545" y="451" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0rd4dca_di" bpmnElement="Event_StartEZZA" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="442" y="458" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="438" y="501" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_11bbcvs_di" bpmnElement="Event_personalDataCollected" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="832" y="458" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="807" y="421" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0jijif8_di" bpmnElement="Gateway_join">
        <dc:Bounds x="1055" y="594" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_057ng9t_di" bpmnElement="Activity_statePZSTAP">
        <dc:Bounds x="520" y="579" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1f25wi2_di" bpmnElement="Gateway_parallel2">
        <dc:Bounds x="815" y="594" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ytoyan_di" bpmnElement="Activity_setApplicationEvent_APPL_IN_UNAUTH_CONCURRENCY">
        <dc:Bounds x="1370" y="700" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01eyjuu_di" bpmnElement="Activity_checkFTPCode">
        <dc:Bounds x="910" y="579" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1x261pw_di" bpmnElement="Activity_setAMLQuestShort">
        <dc:Bounds x="1400" y="454" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0zxhpbz_di" bpmnElement="Flow_0zxhpbz">
        <di:waypoint x="1630" y="494" />
        <di:waypoint x="1662" y="494" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09s0elf_di" bpmnElement="Flow_toSetAMLQuestShort">
        <di:waypoint x="1370" y="494" />
        <di:waypoint x="1400" y="494" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09eqg71_di" bpmnElement="Flow_toZDST">
        <di:waypoint x="1545" y="619" />
        <di:waypoint x="1662" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0c11p92_di" bpmnElement="Flow_FakeFTP">
        <di:waypoint x="1345" y="619" />
        <di:waypoint x="1495" y="619" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1343" y="633" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aoyxlo_di" bpmnElement="Flow_checkApplUserRightsNOK" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1320" y="644" />
        <di:waypoint x="1320" y="740" />
        <di:waypoint x="1370" y="740" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1232" y="712" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1v147st_di" bpmnElement="SequenceFlow_toGetApplWithIncomes">
        <di:waypoint x="1320" y="594" />
        <di:waypoint x="1320" y="534" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tvo7vs_di" bpmnElement="Flow_tocheckApplUserRights">
        <di:waypoint x="1250" y="619" />
        <di:waypoint x="1295" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c9az05_di" bpmnElement="Flow_toStatePZSTAP">
        <di:waypoint x="570" y="501" />
        <di:waypoint x="570" y="579" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z8toa9_di" bpmnElement="Flow_toParallel">
        <di:waypoint x="478" y="476" />
        <di:waypoint x="545" y="476" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ppc8za_di" bpmnElement="Flow_toPersonalDataCollected">
        <di:waypoint x="595" y="476" />
        <di:waypoint x="832" y="476" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p4jbiz_di" bpmnElement="Flow_toZDPDP">
        <di:waypoint x="1105" y="619" />
        <di:waypoint x="1150" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ze8ost_di" bpmnElement="Flow_0ze8ost">
        <di:waypoint x="868" y="476" />
        <di:waypoint x="1080" y="476" />
        <di:waypoint x="1080" y="594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d9tbls_di" bpmnElement="Flow_1d9tbls">
        <di:waypoint x="1010" y="740" />
        <di:waypoint x="1080" y="740" />
        <di:waypoint x="1080" y="644" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0r4getj_di" bpmnElement="SequenceFlow_toSolveApplicant">
        <di:waypoint x="620" y="619" />
        <di:waypoint x="670" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lrl3qq_di" bpmnElement="Flow_toCheckApplUserRights">
        <di:waypoint x="840" y="644" />
        <di:waypoint x="840" y="740" />
        <di:waypoint x="910" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1h0btzv_di" bpmnElement="SequenceFlow_toCheckFTPCode">
        <di:waypoint x="865" y="619" />
        <di:waypoint x="910" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1thepxj_di" bpmnElement="Flow_toParallel2">
        <di:waypoint x="770" y="619" />
        <di:waypoint x="815" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18to5wj_di" bpmnElement="Flow_toJoin2">
        <di:waypoint x="1470" y="740" />
        <di:waypoint x="1520" y="740" />
        <di:waypoint x="1520" y="644" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0h2ia4j_di" bpmnElement="SequenceFlow_0h2ia4j">
        <di:waypoint x="1010" y="619" />
        <di:waypoint x="1055" y="619" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1obmrsb_di" bpmnElement="Flow_toAPPR">
        <di:waypoint x="1500" y="494" />
        <di:waypoint x="1530" y="494" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="Event_StartMainProcess">
        <dc:Bounds x="322" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0utqgmy_di" bpmnElement="Flow_0utqgmy">
        <di:waypoint x="358" y="470" />
        <di:waypoint x="390" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_05xiy62" bpmnElement="Sub_externalPRDN" isExpanded="true">
        <dc:Bounds x="1810" y="1970" width="270" height="120" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1mmw7my" bpmnElement="Event_prdnReceivedMessage" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1852" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel color:color="#000">
          <dc:Bounds x="1836" y="1998" width="68" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ax3aaq_di" bpmnElement="Event_To_Terminate1">
        <dc:Bounds x="2002" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1999" y="2065" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0rclxqr" bpmnElement="Flow_0npdn62">
        <di:waypoint x="1888" y="2040" />
        <di:waypoint x="2002" y="2040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1vxiqg8" bpmnElement="Sub_externalZDZAM" isExpanded="true">
        <dc:Bounds x="1500" y="1970" width="270" height="120" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pxbm1z" bpmnElement="Event_0rarv1v">
        <dc:Bounds x="1692" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1696" y="2065" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wr3p1q" bpmnElement="Event_zdzamReceivedMessage" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1542" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel color:color="#000">
          <dc:Bounds x="1523" y="1998" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1kc1z0f" bpmnElement="Flow_0nlk3ga">
        <di:waypoint x="1578" y="2040" />
        <di:waypoint x="1692" y="2040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1gbf39k" bpmnElement="Sub_externalZDST" isExpanded="true">
        <dc:Bounds x="1190" y="1970" width="270" height="120" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0gtqleu_di" bpmnElement="Event_callZDST2">
        <dc:Bounds x="1382" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1388" y="2065" width="28" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ncfdbm" bpmnElement="Event_stornoReceivedMessage" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1232" y="2022" width="36" height="36" />
        <bpmndi:BPMNLabel color:color="#000">
          <dc:Bounds x="1211" y="1998" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1eljc14_di" bpmnElement="Flow_1eljc14">
        <di:waypoint x="1268" y="2040" />
        <di:waypoint x="1382" y="2040" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0r28k3p_di" bpmnElement="Sub_Rejection" isExpanded="true">
        <dc:Bounds x="790" y="1970" width="360" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fh2h1r_di" bpmnElement="Event_startZDZA">
        <dc:Bounds x="832" y="2042" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="824" y="2085" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1x3q050_di" bpmnElement="Activity_Rejection">
        <dc:Bounds x="920" y="2020" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0t23560_di" bpmnElement="Event_To_Terminate3">
        <dc:Bounds x="1072" y="2042" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1068" y="2085" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ocdd4q_di" bpmnElement="Flow_0ocdd4q">
        <di:waypoint x="1020" y="2060" />
        <di:waypoint x="1072" y="2060" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0urzqzx_di" bpmnElement="Flow_0urzqzx">
        <di:waypoint x="868" y="2060" />
        <di:waypoint x="920" y="2060" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0x9togw_di" bpmnElement="Sub_Cancellation" isExpanded="true">
        <dc:Bounds x="390" y="1970" width="360" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_188eptg_di" bpmnElement="Event_StartZDST">
        <dc:Bounds x="432" y="2042" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="425" y="2085" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11azndf_di" bpmnElement="Activity_Cancellation">
        <dc:Bounds x="520" y="2020" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1pt8pki_di" bpmnElement="Event_To_Terminate2">
        <dc:Bounds x="672" y="2042" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="668" y="2085" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_142kqlw_di" bpmnElement="Flow_142kqlw">
        <di:waypoint x="620" y="2060" />
        <di:waypoint x="672" y="2060" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_020sc6f_di" bpmnElement="Flow_020sc6f">
        <di:waypoint x="468" y="2060" />
        <di:waypoint x="520" y="2060" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0mivjkd_di" bpmnElement="Event_0mivjkd">
        <dc:Bounds x="4932" y="1192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_068bt99_di" bpmnElement="Sub_ContractDocumentSigned" isExpanded="true">
        <dc:Bounds x="3570" y="120" width="560" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0pdheny_di" bpmnElement="Event_contractDocumentSigned">
        <dc:Bounds x="3612" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3587" y="225" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_133stiv_di" bpmnElement="Event_ContractDocumentSigned_event_end">
        <dc:Bounds x="4052" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1lkqv95_di" bpmnElement="Gateway_is_SMS_or_RBklic" isMarkerVisible="true">
        <dc:Bounds x="3825" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1szrc5f_di" bpmnElement="Event_ContractDocumentSigned_noEvent_end">
        <dc:Bounds x="4052" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ju3d78_di" bpmnElement="Activity_clContractSigned_dms">
        <dc:Bounds x="3910" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1upmqld" bpmnElement="Activity_signingChannel_SEL">
        <dc:Bounds x="3690" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0qd81p3_di" bpmnElement="Flow_toGetApplSigningChannelSel">
        <di:waypoint x="3648" y="200" />
        <di:waypoint x="3690" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18q5kbv_di" bpmnElement="Flow_toContractDocumentSigned_event_end">
        <di:waypoint x="4010" y="200" />
        <di:waypoint x="4052" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hyaie1_di" bpmnElement="Flow_1hyaie1">
        <di:waypoint x="3790" y="200" />
        <di:waypoint x="3825" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lxs6nm_di" bpmnElement="Flow_toClContractSigned_dms">
        <di:waypoint x="3875" y="200" />
        <di:waypoint x="3910" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xss0jh_di" bpmnElement="Flow_toContractDocumentSigned_neEvent_end">
        <di:waypoint x="3850" y="225" />
        <di:waypoint x="3850" y="280" />
        <di:waypoint x="4052" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l2k4ul_di" bpmnElement="Flow_1l2k4ul">
        <di:waypoint x="268" y="470" />
        <di:waypoint x="290" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jbybxh_di" bpmnElement="Flow_0jbybxh">
        <di:waypoint x="4890" y="1210" />
        <di:waypoint x="4932" y="1210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1hgimlr_di" bpmnElement="Event_1s9gejz">
        <dc:Bounds x="5052" y="572" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1y4ubv5_di" bpmnElement="Event_1y4ubv5">
        <dc:Bounds x="152" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1s6k7ad_di" bpmnElement="TextAnnotation_1s6k7ad">
        <dc:Bounds x="2680" y="1140" width="100" height="84" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1d965sl_di" bpmnElement="Event_Terminate">
        <dc:Bounds x="4982" y="572" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4977" y="615" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1rsmv9d_di" bpmnElement="Flow_1rsmv9d">
        <di:waypoint x="5018" y="590" />
        <di:waypoint x="5052" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c1up2e_di" bpmnElement="Flow_1c1up2e">
        <di:waypoint x="188" y="470" />
        <di:waypoint x="210" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1m8m0ef_di" bpmnElement="Association_1m8m0ef">
        <di:waypoint x="2730" y="1110" />
        <di:waypoint x="2730" y="1140" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
