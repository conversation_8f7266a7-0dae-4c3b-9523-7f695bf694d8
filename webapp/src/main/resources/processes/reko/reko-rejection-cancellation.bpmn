<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0cmlg86" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="RekoRejectCancel" name="REKO Rejection And Cancellation" isExecutable="true" camunda:historyTimeToLive="7" camunda:isStartableInTasklist="false">
    <bpmn:exclusiveGateway id="Gateway_join1">
      <bpmn:incoming>Flow_09fa8w6</bpmn:incoming>
      <bpmn:incoming>Flow_notStopManualApprovalProcess</bpmn:incoming>
      <bpmn:outgoing>Flow_08db0w0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_manualApproval" name="Manual process opened?" default="Flow_notStopManualApprovalProcess">
      <bpmn:incoming>Flow_toManualApprovalGateway</bpmn:incoming>
      <bpmn:outgoing>Flow_to_randomRetry</bpmn:outgoing>
      <bpmn:outgoing>Flow_notStopManualApprovalProcess</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:parallelGateway id="Gateway_parallel2">
      <bpmn:incoming>Flow_08db0w0</bpmn:incoming>
      <bpmn:incoming>Flow_0a81qfp</bpmn:incoming>
      <bpmn:incoming>Flow_1cvienc</bpmn:incoming>
      <bpmn:incoming>Flow_after_cibis</bpmn:incoming>
      <bpmn:outgoing>Flow_0bb0qj3</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_stopManualApprovalProcess" name="stopManualApprovalProcess" camunda:type="external" camunda:topic="rekoCancelManualApproving">
      <bpmn:extensionElements>
        <camunda:failedJobRetryTimeCycle>${randomRetry}</camunda:failedJobRetryTimeCycle>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_to_stopManualApprovalProcess</bpmn:incoming>
      <bpmn:outgoing>Flow_09fa8w6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="Gateway_parallel1">
      <bpmn:incoming>Flow_0o39fk8</bpmn:incoming>
      <bpmn:outgoing>Flow_toManualApprovalGateway</bpmn:outgoing>
      <bpmn:outgoing>Flow_toOpportunityGateway</bpmn:outgoing>
      <bpmn:outgoing>Flow_toEmailGateway</bpmn:outgoing>
      <bpmn:outgoing>Flow_toTransferDocs</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:exclusiveGateway id="Gateway_loanApplicationPsd2DataExists" default="Flow_notPsdDataDelete">
      <bpmn:incoming>Flow_0bb0qj3</bpmn:incoming>
      <bpmn:outgoing>Flow_to_PSD_data_Delete</bpmn:outgoing>
      <bpmn:outgoing>Flow_notPsdDataDelete</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_join4">
      <bpmn:incoming>Flow_to_join4</bpmn:incoming>
      <bpmn:incoming>Flow_notPsdDataDelete</bpmn:incoming>
      <bpmn:outgoing>Flow_to_cancelDocs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:startEvent id="Event_start" name="Start">
      <bpmn:extensionElements />
      <bpmn:outgoing>Flow_to_checkStorno</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_final" name="final">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_to_final</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09fa8w6" sourceRef="Activity_stopManualApprovalProcess" targetRef="Gateway_join1" />
    <bpmn:sequenceFlow id="Flow_notStopManualApprovalProcess" name="NO" sourceRef="Gateway_manualApproval" targetRef="Gateway_join1">
      <bpmn:extensionElements />
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08db0w0" sourceRef="Gateway_join1" targetRef="Gateway_parallel2" />
    <bpmn:sequenceFlow id="Flow_toManualApprovalGateway" sourceRef="Gateway_parallel1" targetRef="Gateway_manualApproval" />
    <bpmn:sequenceFlow id="Flow_to_randomRetry" name="YES" sourceRef="Gateway_manualApproval" targetRef="Activity_randomRetry">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("manualApprovalStarted") != null &amp;&amp; execution.getVariable("manualApprovalStarted")}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toOpportunityGateway" sourceRef="Gateway_parallel1" targetRef="Gateway_oportunity" />
    <bpmn:sequenceFlow id="Flow_0bb0qj3" sourceRef="Gateway_parallel2" targetRef="Gateway_loanApplicationPsd2DataExists" />
    <bpmn:sequenceFlow id="Flow_to_checkStorno" sourceRef="Event_start" targetRef="Activity_101g3pw" />
    <bpmn:sequenceFlow id="Flow_to_PSD_data_Delete" name="YES" sourceRef="Gateway_loanApplicationPsd2DataExists" targetRef="Activity_PSD_data_Delete">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_notPsdDataDelete" name="NO&#10;" sourceRef="Gateway_loanApplicationPsd2DataExists" targetRef="Gateway_join4">
      <bpmn:extensionElements />
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_to_join4" sourceRef="Activity_PSD_data_Delete" targetRef="Gateway_join4" />
    <bpmn:sequenceFlow id="Flow_to_cancelDocs" sourceRef="Gateway_join4" targetRef="Activity_cancelDocs" />
    <bpmn:sequenceFlow id="Flow_19ovzim" sourceRef="Activity_deleteDocs" targetRef="Gateway_final" />
    <bpmn:serviceTask id="Activity_deleteDocs" name="Delete docs" camunda:type="external" camunda:topic="mtgDeleteDocuments">
      <bpmn:incoming>Flow_to_deleteDocs</bpmn:incoming>
      <bpmn:outgoing>Flow_19ovzim</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_generateEmail_X0000045" name="generateEmail X0000045" camunda:type="external" camunda:topic="rekoGenerateDocument">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="template">X0000045</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_to_generateEmail_X0000045</bpmn:incoming>
      <bpmn:outgoing>Flow_to_createSblActivity_X0000041</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_cancelDocs" name="Cancel docs" camunda:type="external" camunda:topic="cancelDocuments">
      <bpmn:incoming>Flow_to_cancelDocs</bpmn:incoming>
      <bpmn:outgoing>Flow_to_wait60hrsOnLtv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_PSD_data_Delete" name="PSD data Delete" camunda:type="external" camunda:topic="rekoClearPsd2Data">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_to_PSD_data_Delete</bpmn:incoming>
      <bpmn:outgoing>Flow_to_join4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_email" default="Flow_notGenerateEmail">
      <bpmn:incoming>Flow_toEmailGateway</bpmn:incoming>
      <bpmn:outgoing>Flow_to_generateEmail_X0000045</bpmn:outgoing>
      <bpmn:outgoing>Flow_notGenerateEmail</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_to_generateEmail_X0000045" sourceRef="Gateway_email" targetRef="Activity_generateEmail_X0000045">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${rejectionState == "ZDZAM"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_notGenerateEmail" sourceRef="Gateway_email" targetRef="Gateway_join3">
      <bpmn:extensionElements />
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toEmailGateway" sourceRef="Gateway_parallel1" targetRef="Gateway_email" />
    <bpmn:exclusiveGateway id="Gateway_join3">
      <bpmn:incoming>Flow_notGenerateEmail</bpmn:incoming>
      <bpmn:incoming>Flow_1fgxb1t</bpmn:incoming>
      <bpmn:outgoing>Flow_0a81qfp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_to_createSblActivity_X0000041" sourceRef="Activity_generateEmail_X0000045" targetRef="Activity_createSblActivity_X0000041" />
    <bpmn:sequenceFlow id="Flow_0a81qfp" sourceRef="Gateway_join3" targetRef="Gateway_parallel2" />
    <bpmn:intermediateCatchEvent id="Event_wait60hrsOnLtv" name="waiting 60 hours&#10;on LTV">
      <bpmn:incoming>Flow_to_wait60hrsOnLtv</bpmn:incoming>
      <bpmn:outgoing>Flow_to_deleteDocs</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1lypfs6">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${processConfiguration.getValue('waitingOnLTV')}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_to_deleteDocs" sourceRef="Event_wait60hrsOnLtv" targetRef="Activity_deleteDocs" />
    <bpmn:sequenceFlow id="Flow_to_wait60hrsOnLtv" sourceRef="Activity_cancelDocs" targetRef="Event_wait60hrsOnLtv" />
    <bpmn:serviceTask id="Activity_checkFTPCode" name="checkFTPCode" camunda:type="external" camunda:topic="rekoCheckFTPCode">
      <bpmn:incoming>Flow_0cvxgbn</bpmn:incoming>
      <bpmn:outgoing>Flow_0o39fk8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0o39fk8" sourceRef="Activity_checkFTPCode" targetRef="Gateway_parallel1" />
    <bpmn:exclusiveGateway id="Gateway_oportunity" name="is fake client?">
      <bpmn:incoming>Flow_toOpportunityGateway</bpmn:incoming>
      <bpmn:outgoing>Flow_notUpdateOpportunity</bpmn:outgoing>
      <bpmn:outgoing>Flow_to_updateOpportunity</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_notUpdateOpportunity" name="YES" sourceRef="Gateway_oportunity" targetRef="Gateway_join2">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${firstTouchPointFakeClient}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_join2">
      <bpmn:incoming>Flow_notUpdateOpportunity</bpmn:incoming>
      <bpmn:incoming>Flow_0raxumh</bpmn:incoming>
      <bpmn:outgoing>Flow_1cvienc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1cvienc" sourceRef="Gateway_join2" targetRef="Gateway_parallel2" />
    <bpmn:sequenceFlow id="Flow_to_updateOpportunity" name="NO" sourceRef="Gateway_oportunity" targetRef="updateOpportunity">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!firstTouchPointFakeClient}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_rejection" name="has TCHER and  drawn?" default="Flow_rejectionAllowed">
      <bpmn:incoming>Flow_09982c7</bpmn:incoming>
      <bpmn:outgoing>Flow_rejectionAllowed</bpmn:outgoing>
      <bpmn:outgoing>Flow_rejectionNotAllowed</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_rejectionAllowed" name="NO" sourceRef="Gateway_rejection" targetRef="Activity_stateRejectionState">
      <bpmn:extensionElements />
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_rejectionNotAllowed" name="YES" sourceRef="Gateway_rejection" targetRef="Gateway_final">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${applicationState == "SMLAKC" || applicationState == "PRDN" || applicationState == "STR"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_final">
      <bpmn:incoming>Flow_19ovzim</bpmn:incoming>
      <bpmn:incoming>Flow_rejectionNotAllowed</bpmn:incoming>
      <bpmn:outgoing>Flow_to_final</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_to_final" sourceRef="Gateway_final" targetRef="Event_final" />
    <bpmn:scriptTask id="Activity_randomRetry" name="randomRetry" scriptFormat="groovy">
      <bpmn:incoming>Flow_to_randomRetry</bpmn:incoming>
      <bpmn:outgoing>Flow_to_stopManualApprovalProcess</bpmn:outgoing>
      <bpmn:script>import java.util.Random
Random random = new Random()
retry = (random.nextInt(180 + 1 - 20) + 20)
retryStr = "PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S,PT" + retry + "S"
execution.setVariable("randomRetry", retryStr)</bpmn:script>
    </bpmn:scriptTask>
    <bpmn:sequenceFlow id="Flow_to_stopManualApprovalProcess" sourceRef="Activity_randomRetry" targetRef="Activity_stopManualApprovalProcess" />
    <bpmn:serviceTask id="Activity_createSblActivity_X0000041" name="Create SBL Activity" camunda:type="external" camunda:topic="rekoCreateSblActivity">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityEnd">${dateTime().toString()}</camunda:inputParameter>
          <camunda:inputParameter name="activityStatus">Done</camunda:inputParameter>
          <camunda:inputParameter name="activityType">Email - Outbound</camunda:inputParameter>
          <camunda:inputParameter name="activityStart">${dateTime().toString()}</camunda:inputParameter>
          <camunda:inputParameter name="description">email X0000045 - Reko zamítnuto</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_to_createSblActivity_X0000041</bpmn:incoming>
      <bpmn:outgoing>Flow_1fgxb1t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1fgxb1t" sourceRef="Activity_createSblActivity_X0000041" targetRef="Gateway_join3" />
    <bpmn:sequenceFlow id="Flow_toTransferDocs" sourceRef="Gateway_parallel1" targetRef="Activity_transferDocs" />
    <bpmn:sequenceFlow id="Flow_after_cibis" sourceRef="Activity_cibis_cancel" targetRef="Gateway_parallel2" />
    <bpmn:serviceTask id="Activity_stateRejectionState" name="state {rejectionState}" camunda:type="external" camunda:topic="rekoUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">${rejectionState}</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_rejectionAllowed</bpmn:incoming>
      <bpmn:outgoing>Flow_0u46pmx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_101g3pw" name="getAppl with incomes" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="isIncome">1</camunda:inputParameter>
          <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
          <camunda:inputParameter name="isObligation">0</camunda:inputParameter>
          <camunda:inputParameter name="requestedVariants">APR,SEL,OFR,REQ</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_to_checkStorno</bpmn:incoming>
      <bpmn:outgoing>Flow_0gqyq1w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="updateOpportunity" name="updateOpportunity" camunda:type="external" camunda:topic="rekoUpdateOpportunity">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="status">Lost</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_to_updateOpportunity</bpmn:incoming>
      <bpmn:outgoing>Flow_0raxumh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0raxumh" sourceRef="updateOpportunity" targetRef="Gateway_join2" />
    <bpmn:serviceTask id="Activity_cibis_cancel" name="CIBIS - kafka cancel" camunda:type="external" camunda:topic="rekoSendKafkaCibisCancel">
      <bpmn:incoming>Flow_to_cibis</bpmn:incoming>
      <bpmn:outgoing>Flow_after_cibis</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_getSBLPartyDetail" name="getSBLPartyDetail" camunda:type="external" camunda:topic="rekoGetSBLPartyDetail">
      <bpmn:incoming>Flow_toGetSBLParty</bpmn:incoming>
      <bpmn:outgoing>Flow_to_cibis</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_to_cibis" sourceRef="Activity_getSBLPartyDetail" targetRef="Activity_cibis_cancel" />
    <bpmn:sequenceFlow id="Flow_0gqyq1w" sourceRef="Activity_101g3pw" targetRef="Gateway_093rou3" />
    <bpmn:sequenceFlow id="Flow_0u46pmx" sourceRef="Activity_stateRejectionState" targetRef="Activity_TriggerEmailNotification" />
    <bpmn:exclusiveGateway id="Gateway_093rou3" name="callApprovalResult exists?" default="Flow_0lgoobj">
      <bpmn:incoming>Flow_0gqyq1w</bpmn:incoming>
      <bpmn:outgoing>Flow_0lgoobj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wfousq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lgoobj" name="NO" sourceRef="Gateway_093rou3" targetRef="Gateway_1d3bkp0" />
    <bpmn:exclusiveGateway id="Gateway_1d3bkp0">
      <bpmn:incoming>Flow_0lgoobj</bpmn:incoming>
      <bpmn:incoming>Flow_1jlhf5i</bpmn:incoming>
      <bpmn:outgoing>Flow_09982c7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09982c7" sourceRef="Gateway_1d3bkp0" targetRef="Gateway_rejection" />
    <bpmn:serviceTask id="Activity_0rh3uh0" name="generate P0001617" camunda:type="external" camunda:topic="rekoGenerateDocumentV2">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="template">P0001617</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wfousq</bpmn:incoming>
      <bpmn:outgoing>Flow_1jlhf5i</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wfousq" name="YES" sourceRef="Gateway_093rou3" targetRef="Activity_0rh3uh0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("callApprovalResult") != null}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1jlhf5i" sourceRef="Activity_0rh3uh0" targetRef="Gateway_1d3bkp0" />
    <bpmn:serviceTask id="Activity_transferDocs" name="Transfer docs" camunda:type="external" camunda:topic="rekoTransferDocs">
      <bpmn:incoming>Flow_toTransferDocs</bpmn:incoming>
      <bpmn:outgoing>Flow_toGetSBLParty</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_toGetSBLParty" sourceRef="Activity_transferDocs" targetRef="Activity_getSBLPartyDetail" />
    <bpmn:serviceTask id="Activity_TriggerEmailNotification" name="Email notification ZDZAM" camunda:type="external" camunda:topic="rekoTriggerEmailNotification">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="state">ZDZAM</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0u46pmx</bpmn:incoming>
      <bpmn:outgoing>Flow_0cvxgbn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0cvxgbn" sourceRef="Activity_TriggerEmailNotification" targetRef="Activity_checkFTPCode" />
    <bpmn:textAnnotation id="TextAnnotation_14a3ss8">
      <bpmn:text>input variables:
- busApplId
- rejectionState
- clContextId
- correlationId
- opportunityId
- clientId
- applKey
- generatedDocuments
- callApprovalResult</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0mkweys" associationDirection="None" sourceRef="Event_start" targetRef="TextAnnotation_14a3ss8" />
  </bpmn:process>
  <bpmn:escalation id="Escalation_0wx6qxy" name="ZDST" escalationCode="ZDST" />
  <bpmn:escalation id="Escalation_1pdktth" name="ZDST" escalationCode="ZDST" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="RekoRejectCancel">
      <bpmndi:BPMNShape id="Gateway_1if6w2x_di" bpmnElement="Gateway_join1" isMarkerVisible="true">
        <dc:Bounds x="1315" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q797gu_di" bpmnElement="Gateway_manualApproval" isMarkerVisible="true">
        <dc:Bounds x="915" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="841" y="156" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rxk0wj_di" bpmnElement="Gateway_parallel2">
        <dc:Bounds x="1425" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kbi9c5_di" bpmnElement="Activity_stopManualApprovalProcess">
        <dc:Bounds x="1160" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ckpxnn_di" bpmnElement="Gateway_parallel1">
        <dc:Bounds x="795" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09trnka_di" bpmnElement="Gateway_loanApplicationPsd2DataExists" isMarkerVisible="true">
        <dc:Bounds x="1525" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0caz7iu_di" bpmnElement="Gateway_join4" isMarkerVisible="true">
        <dc:Bounds x="1645" y="405" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ihermt_di" bpmnElement="Event_start">
        <dc:Bounds x="152" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="157" y="163" width="25" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1f6iamb_di" bpmnElement="Event_final">
        <dc:Bounds x="2002" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2011" y="225" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bed5mv_di" bpmnElement="Activity_deleteDocs">
        <dc:Bounds x="1750" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0htmkvw_di" bpmnElement="Activity_generateEmail_X0000045">
        <dc:Bounds x="1010" y="500" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1rodyw6_di" bpmnElement="Activity_cancelDocs">
        <dc:Bounds x="1620" y="280" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0p05cbj_di" bpmnElement="Activity_PSD_data_Delete">
        <dc:Bounds x="1500" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dpsnhh_di" bpmnElement="Gateway_email" isMarkerVisible="true">
        <dc:Bounds x="915" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cldh46_di" bpmnElement="Gateway_join3" isMarkerVisible="true">
        <dc:Bounds x="1315" y="515" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tjbqqy_di" bpmnElement="Event_wait60hrsOnLtv">
        <dc:Bounds x="1652" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1630" y="145" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01eyjuu_di" bpmnElement="Activity_checkFTPCode">
        <dc:Bounds x="640" y="500" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uv5vsd_di" bpmnElement="Gateway_oportunity" isMarkerVisible="true">
        <dc:Bounds x="915" y="405" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="907" y="462" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vxktsg_di" bpmnElement="Gateway_join2" isMarkerVisible="true">
        <dc:Bounds x="1315" y="405" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n486a5_di" bpmnElement="Gateway_rejection" isMarkerVisible="true">
        <dc:Bounds x="665" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="725" y="187" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00cabey_di" bpmnElement="Gateway_final" isMarkerVisible="true">
        <dc:Bounds x="1905" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07ekj3v_di" bpmnElement="Activity_randomRetry">
        <dc:Bounds x="1010" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sa35o1" bpmnElement="Activity_createSblActivity_X0000041">
        <dc:Bounds x="1160" y="500" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ejqhjx" bpmnElement="Activity_stateRejectionState">
        <dc:Bounds x="640" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15o6ul6" bpmnElement="Activity_101g3pw">
        <dc:Bounds x="250" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zfnlfj" bpmnElement="updateOpportunity">
        <dc:Bounds x="1010" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mtsvqu_di" bpmnElement="Activity_cibis_cancel">
        <dc:Bounds x="1160" y="660" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1rsoij5_di" bpmnElement="Activity_getSBLPartyDetail">
        <dc:Bounds x="1010" y="660" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_093rou3_di" bpmnElement="Gateway_093rou3" isMarkerVisible="true">
        <dc:Bounds x="385" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="365" y="136" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1d3bkp0_di" bpmnElement="Gateway_1d3bkp0" isMarkerVisible="true">
        <dc:Bounds x="565" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xto1ml" bpmnElement="Activity_0rh3uh0">
        <dc:Bounds x="450" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10p0ps9_di" bpmnElement="Activity_transferDocs">
        <dc:Bounds x="860" y="660" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hs9vj1" bpmnElement="Activity_TriggerEmailNotification" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="640" y="390" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_14a3ss8_di" bpmnElement="TextAnnotation_14a3ss8">
        <dc:Bounds x="120" y="590" width="150" height="154" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_09fa8w6_di" bpmnElement="Flow_09fa8w6">
        <di:waypoint x="1260" y="200" />
        <di:waypoint x="1315" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15s5ozy_di" bpmnElement="Flow_notStopManualApprovalProcess">
        <di:waypoint x="940" y="225" />
        <di:waypoint x="940" y="280" />
        <di:waypoint x="1340" y="280" />
        <di:waypoint x="1340" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1141" y="262" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08db0w0_di" bpmnElement="Flow_08db0w0">
        <di:waypoint x="1365" y="200" />
        <di:waypoint x="1450" y="200" />
        <di:waypoint x="1450" y="515" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k14lh5_di" bpmnElement="Flow_toManualApprovalGateway">
        <di:waypoint x="820" y="515" />
        <di:waypoint x="820" y="200" />
        <di:waypoint x="915" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xsxpk3_di" bpmnElement="Flow_to_randomRetry">
        <di:waypoint x="965" y="200" />
        <di:waypoint x="1010" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="949" y="152" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13vipqg_di" bpmnElement="Flow_toOpportunityGateway">
        <di:waypoint x="828" y="523" />
        <di:waypoint x="870" y="430" />
        <di:waypoint x="915" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bb0qj3_di" bpmnElement="Flow_0bb0qj3">
        <di:waypoint x="1475" y="540" />
        <di:waypoint x="1525" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lutw04_di" bpmnElement="Flow_to_checkStorno">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="250" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hltryi_di" bpmnElement="Flow_to_PSD_data_Delete">
        <di:waypoint x="1550" y="515" />
        <di:waypoint x="1550" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1509" y="493" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03ebzb9_di" bpmnElement="Flow_notPsdDataDelete">
        <di:waypoint x="1575" y="540" />
        <di:waypoint x="1670" y="540" />
        <di:waypoint x="1670" y="455" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1614" y="522" width="17" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oak85t_di" bpmnElement="Flow_to_join4">
        <di:waypoint x="1600" y="430" />
        <di:waypoint x="1645" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0txw071_di" bpmnElement="Flow_to_cancelDocs">
        <di:waypoint x="1670" y="405" />
        <di:waypoint x="1670" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19ovzim_di" bpmnElement="Flow_19ovzim">
        <di:waypoint x="1850" y="200" />
        <di:waypoint x="1905" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wrvipo_di" bpmnElement="Flow_to_generateEmail_X0000045">
        <di:waypoint x="965" y="540" />
        <di:waypoint x="1010" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ny8mz4_di" bpmnElement="Flow_notGenerateEmail">
        <di:waypoint x="940" y="565" />
        <di:waypoint x="940" y="620" />
        <di:waypoint x="1340" y="620" />
        <di:waypoint x="1340" y="565" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04nnxre_di" bpmnElement="Flow_toEmailGateway">
        <di:waypoint x="845" y="540" />
        <di:waypoint x="915" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06xt8zx_di" bpmnElement="Flow_to_createSblActivity_X0000041">
        <di:waypoint x="1110" y="540" />
        <di:waypoint x="1160" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a81qfp_di" bpmnElement="Flow_0a81qfp">
        <di:waypoint x="1365" y="540" />
        <di:waypoint x="1400" y="540" />
        <di:waypoint x="1426" y="539" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c8059p_di" bpmnElement="Flow_to_deleteDocs">
        <di:waypoint x="1688" y="200" />
        <di:waypoint x="1750" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kdzirs_di" bpmnElement="Flow_to_wait60hrsOnLtv">
        <di:waypoint x="1670" y="280" />
        <di:waypoint x="1670" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o39fk8_di" bpmnElement="Flow_0o39fk8">
        <di:waypoint x="740" y="540" />
        <di:waypoint x="795" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ooj025_di" bpmnElement="Flow_notUpdateOpportunity">
        <di:waypoint x="940" y="405" />
        <di:waypoint x="940" y="350" />
        <di:waypoint x="1340" y="350" />
        <di:waypoint x="1340" y="405" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1130" y="332" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cvienc_di" bpmnElement="Flow_1cvienc">
        <di:waypoint x="1365" y="430" />
        <di:waypoint x="1400" y="430" />
        <di:waypoint x="1442" y="523" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jczake_di" bpmnElement="Flow_to_updateOpportunity">
        <di:waypoint x="965" y="430" />
        <di:waypoint x="1010" y="430" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="961" y="403" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0odqvl0_di" bpmnElement="Flow_rejectionAllowed">
        <di:waypoint x="690" y="225" />
        <di:waypoint x="690" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="692" y="240" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1efbu3e_di" bpmnElement="Flow_rejectionNotAllowed">
        <di:waypoint x="690" y="175" />
        <di:waypoint x="690" y="120" />
        <di:waypoint x="1930" y="120" />
        <di:waypoint x="1930" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1300" y="102" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1no1lpz_di" bpmnElement="Flow_to_final">
        <di:waypoint x="1955" y="200" />
        <di:waypoint x="2002" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y62gyp_di" bpmnElement="Flow_to_stopManualApprovalProcess">
        <di:waypoint x="1110" y="200" />
        <di:waypoint x="1160" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fgxb1t_di" bpmnElement="Flow_1fgxb1t">
        <di:waypoint x="1260" y="540" />
        <di:waypoint x="1315" y="540" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yeig5m_di" bpmnElement="Flow_toTransferDocs">
        <di:waypoint x="820" y="565" />
        <di:waypoint x="820" y="700" />
        <di:waypoint x="860" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gxaqxc_di" bpmnElement="Flow_after_cibis">
        <di:waypoint x="1260" y="700" />
        <di:waypoint x="1450" y="700" />
        <di:waypoint x="1450" y="565" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0raxumh_di" bpmnElement="Flow_0raxumh">
        <di:waypoint x="1110" y="430" />
        <di:waypoint x="1315" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09j5g19_di" bpmnElement="Flow_to_cibis">
        <di:waypoint x="1110" y="700" />
        <di:waypoint x="1160" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gqyq1w_di" bpmnElement="Flow_0gqyq1w">
        <di:waypoint x="350" y="200" />
        <di:waypoint x="385" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u46pmx_di" bpmnElement="Flow_0u46pmx">
        <di:waypoint x="690" y="360" />
        <di:waypoint x="690" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lgoobj_di" bpmnElement="Flow_0lgoobj">
        <di:waypoint x="435" y="200" />
        <di:waypoint x="565" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="492" y="182" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09982c7_di" bpmnElement="Flow_09982c7">
        <di:waypoint x="615" y="200" />
        <di:waypoint x="665" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wfousq_di" bpmnElement="Flow_1wfousq">
        <di:waypoint x="410" y="225" />
        <di:waypoint x="410" y="320" />
        <di:waypoint x="450" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="378" y="262" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jlhf5i_di" bpmnElement="Flow_1jlhf5i">
        <di:waypoint x="550" y="320" />
        <di:waypoint x="590" y="320" />
        <di:waypoint x="590" y="225" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zg6hh2_di" bpmnElement="Flow_toGetSBLParty">
        <di:waypoint x="960" y="700" />
        <di:waypoint x="1010" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0mkweys_di" bpmnElement="Association_0mkweys">
        <di:waypoint x="170" y="218" />
        <di:waypoint x="170" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cvxgbn_di" bpmnElement="Flow_0cvxgbn">
        <di:waypoint x="690" y="470" />
        <di:waypoint x="690" y="500" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
