package cz.equa.camapp.delegate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import cz.equa.camapp.config.FreeMarkerConfig;
import cz.equa.camapp.model.DealerInfoDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.GetLoanApplicationResult;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.HadPartnersService;
import cz.equa.camapp.service.FreeMarkerTemplateService;
import cz.equa.camapp.service.applicationservice.model.GetMortApplResponseDTO;
import cz.equa.camapp.utils.ApplicationUtils;
import cz.rb.las.application.model.CtGetLoanAppl;
import cz.rb.las.application.model.CtPersonGet;
import freemarker.template.TemplateException;
import jakarta.mail.internet.MimeMessage;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static cz.equa.camapp.delegate.SendEmailNotificationDelegate.*;
import static cz.equa.camapp.process.ProcessVariable.FTP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith({SpringExtension.class})
@SpringJUnitWebConfig(classes = {SendEmailNotificationDelegate.class, FreeMarkerTemplateService.class, FreeMarkerConfig.class})
class SendEmailNotificationDelegateTest {

    @Mock
    private DelegateExecution delegateExecution;

    @MockBean
    public ApplicationService applicationService;

    @MockBean
    public HadPartnersService hadPartnersService;

    @InjectMocks
    private FreeMarkerTemplateService templateService;

    @MockBean
    protected JavaMailSender javaMailSender;

    @Autowired
    SendEmailNotificationDelegate underTest;

    @Test
    void getTemplateName() {
        assertNull(underTest.getTemplateName("NBDV", "RML_", ""));
        assertNull(underTest.getTemplateName("NBDV", "", "email"));
        assertNull(underTest.getTemplateName("", "RML_", "email"));
        assertNull(underTest.getTemplateName("NBDU", "RDD_", "email"));
        assertEquals("ZDZAM", underTest.getTemplateName("ZDZAM", "RCC_STYLE", "email"));
        assertEquals("NBDV", underTest.getTemplateName("NBDV", "RCC_STYLE", "email"));
        assertEquals("NBDV", underTest.getTemplateName("NBDV", "ROD_INFRE", "email"));
        assertEquals("NBDU", underTest.getTemplateName("NBDU", "ROD_INFRE", "email"));
        assertEquals("NBDV", underTest.getTemplateName("NBDV", "ROD_INFRE", "email"));
        assertEquals("FNLEND", underTest.getTemplateName("FNLEND", "RCL_REFI", "email"));
        assertNull(underTest.getTemplateName("FNLSCR", "RCL_REFI", "email"));

        assertEquals("RML-GNV", underTest.getTemplateName("GNV", "RML_CLAS", "email"));
        assertEquals("RML-NBDV", underTest.getTemplateName("NBDV", "RML_CLAS", "email"));
        assertEquals("RML-NBDU", underTest.getTemplateName("NBDU", "RML_CLAS", "email"));
        assertEquals("RML-FNLSCR", underTest.getTemplateName("FNLSCR", "RML_CLAS", "email"));
        assertEquals("RML-STR", underTest.getTemplateName("STR", "RML_CLAS", "email"));
        assertEquals("RML-ZDZAM", underTest.getTemplateName("ZDZAM", "RML_CLAS", "email"));
        assertNull(underTest.getTemplateName("FNLEND", "RML_CLAS", "email"));
        assertEquals("RML-FNLSCR", underTest.getTemplateName("SN", "RML_CLAS", "email"));
        assertEquals("BSL-NBDV", underTest.getTemplateName("NBDV", "BSL_REA", "email"));
        assertEquals("BSL-NBDV", underTest.getTemplateName("NBDV", "BSL_DRA", "email"));
        assertEquals("BSL-ZDZAM", underTest.getTemplateName("ZDZAM", "BSL_REA", "email"));
        assertEquals("BSL-ZDZAM", underTest.getTemplateName("ZDZAM", "BSL_DRA", "email"));
    }

    protected DealerInfoDTO prepareDealerInfoDTO() {
        DealerInfoDTO di = new DealerInfoDTO();
        di.setBroker(true);
        di.setFirstName("Name");
        di.setLastName("Surname");
        di.setCompanyName("Company name");
        di.setCompanyIco("1234567890");
        di.setEmail("<EMAIL>");
        return di;
    }

    @Test
    void mapData() throws Exception {
        doReturn(null).when(delegateExecution).getVariable(ProcessVariable.TCHER_EVENT.getKey());
        doReturn("NBDV").when(delegateExecution).getVariable(ProcessVariable.APPLICATION_STATE.getKey());
        doReturn("ADD_UNSECURED_LOAN").when(delegateExecution).getVariable(ProcessVariable.PROCESS_TP.getKey());
        doReturn("RCL_STANDARD").when(delegateExecution).getVariable(ProcessVariable.PRODUCT_ID.getKey());
        doReturn("<EMAIL>").when(delegateExecution).getVariable(ProcessVariable.DEALER_EMAIL.getKey());
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());
        assertDoesNotThrow(() -> underTest.execute(delegateExecution));
    }

    @Test
    void renderBody() throws TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, PRODUCT);
        model.put(APPL_TP_ID, "ApplTp");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        model.put(RECIPIENT_ROLE, "EFA");
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        String subject = underTest.renderSubject("NBDV", model);
        assertThat(subject).contains(PRODUCT);
        String body = underTest.renderBody("NBDV", model);
        assertThat(body)
                .contains(FULL_NAME, PARTY_ID, PRODUCT, "1 123 123,45", APPROVAL_DATE)
                .doesNotContain("Číslo žádosti:");
    }

    @Test
    void renderBodyRmlZdzam() throws TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, "RML_CLAS");
        model.put(APPL_ID, "123");
        model.put(APPL_TP_ID, "ApplTp");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        model.put(RECIPIENT_ROLE, "EFA");
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        String subject = underTest.renderSubject("RML-ZDZAM", model);
        assertThat(subject).contains("RML_CLAS");
        String body = underTest.renderBody("RML-ZDZAM", model);
        assertThat(body)
                .contains("že žádost uvedená níže byla zamítnuta");
    }

    @Test
    void renderBodyRmlNbdv() throws TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, "RML_CLAS");
        model.put(APPL_ID, "123");
        model.put(APPL_TP_ID, "ApplTp");
        model.put(CREATOR_NAME, "creatorName");
        model.put(CREATOR_COMPANY, "creatorCompany");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        model.put(RECIPIENT_ROLE, "EFA");
        model.put(FTP_ROLE, "EFA");
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        String subject = underTest.renderSubject("RML-NBDV", model);
        assertThat(subject).contains("RML_CLAS");
        String body = underTest.renderBody("RML-NBDV", model);
        assertThat(body)
                .contains("Schválená částka:")
                .contains("Vytvořil - jméno:")
                .contains("Vytvořil - HC/síť:");
    }

    @Test
    void renderBodyBslNbdv() throws TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, "BSL_REA");
        model.put(APPL_ID, "123");
        model.put(APPL_TP_ID, "ApplTp");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        model.put(RECIPIENT_ROLE, "BANKER");
        model.put(FTP_ROLE, "BANKER");
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        String subject = underTest.renderSubject("BSL-NBDV", model);
        assertThat(subject).contains("BSL_REA");
        String body = underTest.renderBody("BSL-NBDV", model);
        assertThat(body)
                .contains("Schválená částka:")
                .contains("Žádost je možné nyní dokončit.")
;
    }

    @Test
    void renderBodyRmlNbdvClient() throws TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, "RML_CLAS");
        model.put(APPL_ID, "123");
        model.put(APPL_TP_ID, "ApplTp");
        model.put(CREATOR_NAME, "creatorName");
        model.put(CREATOR_COMPANY, "creatorCompany");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        model.put(RECIPIENT_ROLE, "EFA");
        model.put(FTP_ROLE, "CLIENT");
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        String subject = underTest.renderSubject("RML-NBDV", model);
        assertThat(subject).contains("RML_CLAS");
        String body = underTest.renderBody("RML-NBDV", model);
        assertThat(body)
                .contains("Schválená částka:")
                .contains("Vytvořil - jméno:")
                .contains("Klient")
                .doesNotContain("Vytvořil - HC/síť:");
    }

    @Test
    void renderBodyWithDates() throws TemplateException, IOException {
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());
        Map<String, Object> model = new HashMap<>();
        model.put(FULL_NAME, FULL_NAME);
        model.put(PARTY_ID, PARTY_ID);
        model.put(PRODUCT, PRODUCT);
        model.put(APPL_ID, "123");
        model.put(APPL_TP_ID, "ApplTp");
        model.put(AMOUNT, new BigDecimal("1123123.45"));
        model.put(REPRE_DRAW_DATE, LocalDate.now());
        model.put(APPROVAL_DATE, APPROVAL_DATE);
        String subject = underTest.renderSubject("PRDN", model);
        assertThat(subject).contains(PRODUCT);
        String body = underTest.renderBody("PRDN", model);
        assertThat(body).contains(FULL_NAME, PRODUCT, "1 123 123,45", "Datum čerpání");
    }

    @Test
    void sendRmlStr() throws Exception {
        MimeMessage mimeMessage = mock(MimeMessage.class);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        when(delegateExecution.getVariable(ProcessVariable.APPLICATION_KEY.getKey())).thenReturn(853599096714L);
        when(delegateExecution.getVariable(ProcessVariable.APPLICATION_STATE.getKey())).thenReturn("STR");
        when(delegateExecution.getVariable(ProcessVariable.PROCESS_TP.getKey())).thenReturn(null);
        when(delegateExecution.getVariable(ProcessVariable.PRODUCT_ID.getKey())).thenReturn("RML_");
        when(delegateExecution.getVariable(FTP.getKey())).thenReturn("10000000");
        when(delegateExecution.getVariable(ProcessVariable.RECIPIENT_ID.getKey())).thenReturn("30000000");
        when(delegateExecution.getVariable(ProcessVariable.BUS_APPL_ID.getKey())).thenReturn("2580036846");
        when(delegateExecution.getVariable(ProcessVariable.BUS_SUB_APPL_ID.getKey())).thenReturn("2580036846-01");
        when(applicationService.getMortgageAppl(any(), any(), any(), any())).thenReturn(getGetMortApplResponseDTO());

        assertDoesNotThrow(() -> underTest.execute(delegateExecution));
    }

    @Test
    void sendClFnlend() throws Exception {
        MimeMessage mimeMessage = mock(MimeMessage.class);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));
        when(hadPartnersService.getDealerDetailWithCache(any(), anyString())).thenReturn(prepareDealerInfoDTO());

        when(delegateExecution.getVariable(ProcessVariable.APPLICATION_KEY.getKey())).thenReturn(853599097968L);
        when(delegateExecution.getVariable(ProcessVariable.APPLICATION_STATE.getKey())).thenReturn("FNLEND");
        when(delegateExecution.getVariable(ProcessVariable.PROCESS_TP.getKey())).thenReturn("ADD_UNSECURED_LOAN");
        when(delegateExecution.getVariable(ProcessVariable.PRODUCT_ID.getKey())).thenReturn("RCL_REFI");
        when(delegateExecution.getVariable(ProcessVariable.DEALER_EMAIL.getKey())).thenReturn("<EMAIL>");
        when(delegateExecution.getVariable(ProcessVariable.BUS_APPL_ID.getKey())).thenReturn("2409443088");
        when(applicationService.getApplicationWithProperties(anyLong(), anyBoolean(), anyBoolean(), anyList(), anyBoolean(), anyString())).thenReturn(getRclRefiDTO());

        assertDoesNotThrow(() -> underTest.execute(delegateExecution));
    }

    private GetMortApplResponseDTO getGetMortApplResponseDTO() throws JsonProcessingException {
        String json = """
                {"applId":{"applKey":4032324,"busApplId":"2580036846"},"persons":[{"siebelId":"22667112","genderId":"M","eduStatId":"B","housingStatId":"OWNFL","citizenship":"CZ","marStatId":"M","rcNum":"700929/0321","salutId":null,"titleBefore":"Ing.","firstName":"Martin","familyName":"Koutský","birthPlace":"Praha 4","birthName":"Koutský","birthDate":"1970-09-29","birthCntryId":"CZ","taxDomicile":"CZ","tin":null,"pep":false,"mortExpSum":null,"householdExpSum":null,"othExpSum":null,"mntdChildCnt":null,"crntAddrValidDate":"2020-02-12","email":"<EMAIL>","phoneNum":"+420723763230","permAddr":{"streetName":"Tusarova","streetNum":"1316/17a","cityName":"Praha","detail":null,"zip":"17000","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":5743115,"addrSince":null},"postalAddr":{"streetName":"Kolbenova","streetNum":"882/5a","cityName":"Praha 9","detail":null,"zip":"19000","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":5743116,"addrSince":null},"idCards":[{"idCardTpId":"6","idCardPurpId":"1STID","idCardAppResultId":null,"cardIssuerCntryId":"CZ","issueDate":"2023-03-06","issuer":"Praha 7","exprDate":"2033-03-06","cardId":"*********","cardValidityLimitedFlag":null,"investigationId":null,"chngChnlRsn":null,"unapprRsn":null}],"adbInstPtKey":3842666,"ptTpId":"FO","cbId":"9003869","verifications":[],"incomeVerifications":[],"ptCorpDetail":null,"busName":null,"ptStatId":"CRM_SYNCED","appctAge":54,"applDataSeg":[],"euFlag":true,"rsdntFlag":null}],"mortgageAppls":[{"applId":{"applKey":4032324,"busApplId":"2580036846"},"applTpId":"NEW_PROD","applStatId":"NBDV","applDate":"2025-02-12T13:27:56Z","posId":"18000000","firstTouchPoint":"30520606","applReason":null,"hash":"Ulxa9HIsEsAEniucGFgwKOWqiT4mKUUkTyqgRJnDTBB1yEL2qAh2FLdpI/18ueSmB7Z+OtfXacgyT7ZLY6PHCEOk2NQUaIiwRjmUu215XulN1ppQRiw9d9NII9YqmZ/cRThmS0rnKPcl1LYIh9nEv5+VV7UlTk4Ecdl5wcH+YIY=","preApprOfrId":null,"opportunityId":"54788973","ccyId":"CZK","distCnlId":"14","advisorId":null,"fulfillmentCnlId":null,"wrkPlaceId":"ec827133-ded1-439d-9d02-2e2401c2c202","busProdSubTp":"RML_CLAS","browserInfo":null,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"firstTouchPointOwnr":"30520606","promoCode":null,"applDsnKey":null,"applComplPosId":null,"contrNum":null,"contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"30520606","rejectRsn":null,"rejectRsnTp":null,"fiOperCode":null,"registryResult":null,"finalRiskClass":null,"busProdTp":"RML","applDateTo":"2025-05-13T23:00:00Z","prntApplKey":null,"prntApplBusApplId":null,"appctAgeRangeId":null,"coappAgeRangeId":null,"firstTouchPointBnkr":"10333047","mortCentrePosId":"RBPHKARL","extnApplId":null,"complCnlId":null,"appctRole":"HYFE_ADVISOR","persons":[{"adbInstPtKey":3842666,"applPtRoleTpId":"APPLICANT","relationToApplicant":null,"extnRejRsnTpDescr":null,"rejectRsnTpIds":null,"scgExpsSum":null,"scgMortInstlSum":null,"scgInstlSum":null,"scgFinalRiskClassId":null,"scgAppctHighestIncFlag":null,"regRiskClassId":null,"regRiskBandId":null,"apprProcessPartId":null,"dsnTpId":null}],"chieldAppls":[{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":true,"chieldApplApplRelTpId":"SUBAPPL"},{"chieldApplKey":4032326,"chieldApplBusApplId":"2580036846-02","chieldApplStatId":"NBDV","chieldApplPrimFlag":true,"chieldApplApplRelTpId":"SUBAPPL"}],"households":[],"applVariants":[],"applMetadata":[],"incomes":[],"applObligations":[],"applDecisions":[]},{"applId":{"applKey":4032325,"busApplId":"2580036846-01"},"applTpId":"SUBAPPL_GOFR","applStatId":"GNUPL","applDate":"2025-02-12T13:27:56Z","posId":null,"firstTouchPoint":"30520606","applReason":null,"hash":null,"preApprOfrId":null,"opportunityId":null,"ccyId":"CZK","distCnlId":"14","advisorId":null,"fulfillmentCnlId":null,"wrkPlaceId":null,"busProdSubTp":"RML_CLAS","browserInfo":null,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"firstTouchPointOwnr":"30520606","promoCode":null,"applDsnKey":"4834170","applComplPosId":null,"contrNum":null,"contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"30520606","rejectRsn":null,"rejectRsnTp":null,"fiOperCode":null,"registryResult":null,"finalRiskClass":null,"busProdTp":"RML","applDateTo":"2025-03-14T23:00:00Z","prntApplKey":4032324,"prntApplBusApplId":"2580036846","appctAgeRangeId":"AGE1836","coappAgeRangeId":null,"firstTouchPointBnkr":"10333047","mortCentrePosId":"RBPHKARL","extnApplId":null,"complCnlId":null,"appctRole":"HYFE_ADVISOR","persons":[{"adbInstPtKey":3842666,"applPtRoleTpId":"APPLICANT","relationToApplicant":null,"extnRejRsnTpDescr":null,"rejectRsnTpIds":null,"scgExpsSum":0,"scgMortInstlSum":0,"scgInstlSum":0,"scgFinalRiskClassId":null,"scgAppctHighestIncFlag":true,"regRiskClassId":null,"regRiskBandId":null,"apprProcessPartId":"RML_GOFR","dsnTpId":"APPROVE"}],"chieldAppls":[{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":false,"chieldApplApplRelTpId":"SUBAPPL"},{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":false,"chieldApplApplRelTpId":"CLONE"},{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":false,"chieldApplApplRelTpId":"GOFR"}],"households":[],"applVariants":[{"applVariantKey":7773757,"applVariantTpId":"GAR","applVariantSignCnlFlag":null,"busProdTpId":"RML","busProdSubTpId":"RML_CLAS","finaAmt":2000000,"instlAmt":15674,"instlCnt":186,"maturityDate":null,"intrsRx":0.0519,"rpsn":0.0546,"camCode":null,"minAvlblAmt":500000,"minInstlAmt":1100,"minInstlCnt":60,"maxAvlblAmt":2680000,"maxInstlAmt":60000,"maxInstlCnt":186,"upsellAmt":0,"maxUpsellAmt":0,"ccyId":"CZK","totRpmtAmt":2946120.61,"refiSavedAmt":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"calcltrTpId":null,"intrsRxOrig":null,"minUpsellAmt":null,"rltPrice":3350000,"minRltPrice":2500000,"oblgtnAmt":0,"maxOblgtnAmt":null,"maturityExt":0,"dispoIncStrsAppcts":null,"appctsCnt":null,"ltv":0.597,"minLtv":null,"maxLtv":0.8,"intrsRxTpId":"US_CLAS_PROFI","intrsRxFixPerTpId":"3","intnCaFlag":null,"respMortFlag":false,"instlDay":null,"maxAvlblAmtWoLtv":2680000,"ddnPer":1,"applVariantFees":[],"applVariantInsurances":[],"applVariantSurcharges":[],"applVariantPurposes":[],"applVariantComments":[],"hycProdTpId":null,"hycProdTpName":null,"busProdSubTpDesignName":null,"intrsRxFixDate":null},{"applVariantKey":7773756,"applVariantTpId":"REQ","applVariantSignCnlFlag":null,"busProdTpId":"RML","busProdSubTpId":"RML_CLAS","finaAmt":2000000,"instlAmt":15674,"instlCnt":186,"maturityDate":null,"intrsRx":0.0519,"rpsn":0.055,"camCode":null,"minAvlblAmt":null,"minInstlAmt":null,"minInstlCnt":null,"maxAvlblAmt":null,"maxInstlAmt":null,"maxInstlCnt":null,"upsellAmt":null,"maxUpsellAmt":null,"ccyId":"CZK","totRpmtAmt":2946121,"refiSavedAmt":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"calcltrTpId":"RML_GOFR_CALCULATOR_EXT","intrsRxOrig":0.0619,"minUpsellAmt":null,"rltPrice":3350000,"minRltPrice":null,"oblgtnAmt":null,"maxOblgtnAmt":null,"maturityExt":null,"dispoIncStrsAppcts":null,"appctsCnt":null,"ltv":0.597,"minLtv":null,"maxLtv":null,"intrsRxTpId":"US_CLAS_PROFI","intrsRxFixPerTpId":"3","intnCaFlag":null,"respMortFlag":false,"instlDay":null,"maxAvlblAmtWoLtv":null,"ddnPer":1,"applVariantFees":[],"applVariantInsurances":[],"applVariantSurcharges":[],"applVariantPurposes":[],"applVariantComments":[],"hycProdTpId":null,"hycProdTpName":null,"busProdSubTpDesignName":null,"intrsRxFixDate":null}],"applMetadata":[],"incomes":[],"applObligations":[],"applDecisions":[{"apprProcessPartId":"RML_GOFR","dsnTpId":"APPROVE","crntFlag":true,"maxDsti":0.6,"maxDti":99,"applDsti":0.156,"applDti":1.666,"decrExp":null,"netIncSumFinal":100000,"expSumFinal":4860,"appctsAge":54,"instlSumFinal":0,"instlSumMortFinal":0,"expstSumFinal":4860}]},{"applId":{"applKey":4032326,"busApplId":"2580036846-02"},"applTpId":"SUBAPPL_SEL","applStatId":"NBDV","applDate":"2025-02-12T13:27:56Z","posId":null,"firstTouchPoint":"30520606","applReason":null,"hash":null,"preApprOfrId":null,"opportunityId":"54788976","ccyId":"CZK","distCnlId":"14","advisorId":null,"fulfillmentCnlId":null,"wrkPlaceId":null,"busProdSubTp":"RML_CLAS","browserInfo":null,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"firstTouchPointOwnr":"30520606","promoCode":null,"applDsnKey":"4834172","applComplPosId":null,"contrNum":null,"contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"30520606","rejectRsn":null,"rejectRsnTp":null,"fiOperCode":null,"registryResult":"0","finalRiskClass":null,"busProdTp":"RML","applDateTo":"2025-05-13T23:00:00Z","prntApplKey":null,"prntApplBusApplId":null,"appctAgeRangeId":"AGE1836","coappAgeRangeId":null,"firstTouchPointBnkr":"10333047","mortCentrePosId":"RBPHKARL","extnApplId":null,"complCnlId":null,"appctRole":"HYFE_ADVISOR","persons":[{"adbInstPtKey":3842666,"applPtRoleTpId":"APPLICANT","relationToApplicant":null,"extnRejRsnTpDescr":null,"rejectRsnTpIds":null,"scgExpsSum":100000,"scgMortInstlSum":0,"scgInstlSum":2300,"scgFinalRiskClassId":null,"scgAppctHighestIncFlag":true,"regRiskClassId":null,"regRiskBandId":"0","apprProcessPartId":"1ST_SCORING","dsnTpId":"APPROVE"}],"chieldAppls":[{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":true,"chieldApplApplRelTpId":"CLONE"},{"chieldApplKey":4032325,"chieldApplBusApplId":"2580036846-01","chieldApplStatId":"GNUPL","chieldApplPrimFlag":true,"chieldApplApplRelTpId":"GOFR"},{"chieldApplKey":4032326,"chieldApplBusApplId":"2580036846-02","chieldApplStatId":"NBDV","chieldApplPrimFlag":false,"chieldApplApplRelTpId":"SUBAPPL"}],"households":[],"applVariants":[{"applVariantKey":7773759,"applVariantTpId":"REQ","applVariantSignCnlFlag":null,"busProdTpId":"RML","busProdSubTpId":"RML_CLAS","finaAmt":2000000,"instlAmt":15674,"instlCnt":186,"maturityDate":null,"intrsRx":0.0519,"rpsn":0.0546,"camCode":null,"minAvlblAmt":500000,"minInstlAmt":1100,"minInstlCnt":60,"maxAvlblAmt":2680000,"maxInstlAmt":60000,"maxInstlCnt":186,"upsellAmt":0,"maxUpsellAmt":0,"ccyId":"CZK","totRpmtAmt":2946120.61,"refiSavedAmt":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"calcltrTpId":null,"intrsRxOrig":null,"minUpsellAmt":null,"rltPrice":3350000,"minRltPrice":2500000,"oblgtnAmt":0,"maxOblgtnAmt":null,"maturityExt":0,"dispoIncStrsAppcts":null,"appctsCnt":null,"ltv":0.597,"minLtv":null,"maxLtv":0.8,"intrsRxTpId":"US_CLAS_PROFI","intrsRxFixPerTpId":"3","intnCaFlag":null,"respMortFlag":false,"instlDay":null,"maxAvlblAmtWoLtv":null,"ddnPer":1,"applVariantFees":[],"applVariantInsurances":[],"applVariantSurcharges":[],"applVariantPurposes":[],"applVariantComments":[],"hycProdTpId":null,"hycProdTpName":null,"busProdSubTpDesignName":null,"intrsRxFixDate":"2025-02-12"},{"applVariantKey":7773761,"applVariantTpId":"OFR","applVariantSignCnlFlag":null,"busProdTpId":"RML","busProdSubTpId":"RML_CLAS","finaAmt":2000000,"instlAmt":15674,"instlCnt":186,"maturityDate":null,"intrsRx":0.0519,"rpsn":0.0546,"camCode":null,"minAvlblAmt":500000,"minInstlAmt":1100,"minInstlCnt":60,"maxAvlblAmt":2345000,"maxInstlAmt":55900,"maxInstlCnt":186,"upsellAmt":0,"maxUpsellAmt":0,"ccyId":"CZK","totRpmtAmt":2946120.61,"refiSavedAmt":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"calcltrTpId":null,"intrsRxOrig":null,"minUpsellAmt":null,"rltPrice":3350000,"minRltPrice":2500000,"oblgtnAmt":0,"maxOblgtnAmt":null,"maturityExt":0,"dispoIncStrsAppcts":null,"appctsCnt":null,"ltv":0.597,"minLtv":null,"maxLtv":0.8,"intrsRxTpId":"US_CLAS_PROFI","intrsRxFixPerTpId":"3","intnCaFlag":null,"respMortFlag":false,"instlDay":null,"maxAvlblAmtWoLtv":7133000,"ddnPer":1,"applVariantFees":[],"applVariantInsurances":[],"applVariantSurcharges":[],"applVariantPurposes":[],"applVariantComments":[],"hycProdTpId":null,"hycProdTpName":null,"busProdSubTpDesignName":null,"intrsRxFixDate":null}],"applMetadata":[],"incomes":[],"applObligations":[],"applDecisions":[{"apprProcessPartId":"1ST_SCORING","dsnTpId":"APPROVE","crntFlag":true,"maxDsti":0.6,"maxDti":99,"applDsti":0.185,"applDti":1.804,"decrExp":null,"netIncSumFinal":97000,"expSumFinal":4860,"appctsAge":54,"instlSumFinal":2300,"instlSumMortFinal":0,"expstSumFinal":4860}]}]}
                """;
        return ApplicationUtils.convertJsonToType(json, GetMortApplResponseDTO.class);
    }

    private GetLoanApplicationResult getRclRefiDTO() throws JsonProcessingException {
        String jsonCtGetLoanAppl = """
                {"applTpId":"NEW_PROD","applStatId":"FNLEND","applDate":"2024-09-09T11:06:26Z","posId":"18000000","firstTouchPoint":"********","applReason":null,"hash":"LlZSOmERwsuHv8I41ANUI02RbOxOHatBxW0zlco9QaovcRi7mB4EbF1gKB5oxBInxVTF9hfdgVCaY7SeKyf48JLUUfTIbaMoaejqfpi1O3EjlG6UrbEEC4RgowAEdpwRFnPfeJljFbEoV8ea5Vm1O+M89XQDv5jaGcvQ/eRFEXg=","preApprOfrId":null,"opportunityId":"46088021","fullApplFlag":true,"ccyId":"CZK","distCnlId":"14","advisorId":null,"advisorName":null,"fulfillmentCnlId":null,"wrkPlaceId":"2c03ea33-42c7-4015-a554-b32f5568fac2","busProdSubTp":"RCL_REFI","browserInfo":null,"receivedTraderName":null,"pushNotifPermissionFlag":null,"activationId":null,"contAccessReqFlag":null,"contAccessAlwFlag":null,"contAccessScreenId":null,"dscntIntrs":0,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"orderNr":null,"url":null,"segments":["C","B","A"],"firstTouchPointOwnr":"********","promoCode":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"applDsnKey":"*************","applComplPosId":null,"contrNum":null,"contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"********","validFromDate":"2024-09-09T11:06:26Z","validToDate":"2024-10-09T22:00:00Z","moneyTransferDate":null,"rejectRsnId":null,"rejectRsn":null,"rejectRsnTp":null,"personApplModelId":null,"computedSalary":39064,"fiOperCode":null,"incVerifPSD2Flag":false,"incVerifAccStmFlag":false,"incVerifStmUploadFlag":false,"incVerifCallFlag":false,"incVerifSrcId":null,"incVerifPSD2Discount":-0.3,"contrCond":"Vaši úvěruschopnost jsme posoudili na základě údajů uvedených ve Vaší žádosti, s přihlédnutím k statistickým podkladům, údajům z rejstříků a úvěrových registrů. Váš příjem jsme ověřili s využitím komplexního statistického nástroje, který určuje reálně dosažitelné příjmy na základě Vašich osobních předpokladů. Podle našeho vyhodnocení Vám na splátku nové půjčky zbývají dostatečné prostředky a jste tedy schopen/schopna půjčku splácet. Rovněž další vyhodnocení, která jsme provedli, nasvědčují tomu, že půjčku budete bez problémů splácet. Pokud by nastaly nějaké skutečnosti, které jsme podle Vás nezhodnotili, a které by vedly k tomu, že nebudete moci půjčku splácet, návrh nepodepisujte a kontaktujte nás.","registryResult":"0","finalRiskClass":null}
                """;
        String jsonCtPrimaryOwner = """
                {"siebelId":"27406953","genderId":"M","eduStatId":null,"housingStatId":null,"citizenship":"CZ","marStatId":null,"rcNum":"880209/4290","salutId":null,"titleBefore":null,"firstName":"Petr","familyName":"Starý","birthPlace":"Brno","birthName":"Starý","birthDate":"1988-02-09","birthCntryId":"CZ","taxDomicile":null,"tin":null,"pep":false,"mortExpSum":3000,"householdExpSum":2000,"othExpSum":1000,"crntAddrValidDate":null,"email":"<EMAIL>","phoneNum":"+420775351812","permAddr":{"streetName":"Nádražní","streetNum":"832/64","cityName":"Velké Pavlovice","detail":null,"zip":"19000","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":1542421,"addrSince":null},"postalAddr":{"streetName":"Nádražní","streetNum":"832/64","cityName":"Velké Pavlovice","detail":null,"zip":"19000","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":1542422,"addrSince":null},"idCards":[{"idCardTpId":"6","idCardPurpId":"1STID","idCardAppResultId":null,"cardIssuerCntryId":"CZ","issueDate":"2021-12-13","issuer":"MěÚ HUSTOPEČE","exprDate":"2031-12-13","cardId":"*********","cardValidityLimitedFlag":null,"investigationId":null,"chngChnlRsn":null,"unapprRsn":null}],"adbInstPtKey":905261,"ptTpId":"FO","cbId":"8763503","verifications":null,"incomeVerifications":null,"ptCorpDetail":{"ptAcntgTpId":null,"entpInc":null,"taxYear":null,"dtiAmt":null,"dprAmt":null,"taxFlatRx":null,"equityAmt":null,"prftLossAmt":null,"prevRvnAmt":null,"rvnAmt":null,"ast":null},"busName":null}
                """;
        String jsonCtApplVariants = """
                [{"loanPurpId":null,"applVariantKey":*************,"applVariantTpId":"SEL","applVariantSignCnlFlag":null,"busProdTpId":"RCL","busProdSubTpId":"RCL_REFI","finaAmt":147000,"instlAmt":1855,"instlCnt":120,"maturityDate":"2034-09-09","intrsRx":0.089,"rpsn":0.0928,"camCode":null,"minAvlblAmt":null,"minInstlAmt":null,"minInstlCnt":null,"maxAvlblAmt":null,"maxInstlAmt":null,"maxInstlCnt":null,"upsellAmt":0,"maxUpsellAmt":null,"ccyId":"CZK","totRpmtAmt":222443.69,"refiSavedAmt":null,"accNumPrefix":null,"accNum":"**********","accBankCode":"5500","actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":0.089,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null},{"loanPurpId":null,"applVariantKey":*************,"applVariantTpId":"OFR","applVariantSignCnlFlag":null,"busProdTpId":"RCL","busProdSubTpId":"RCL_REFI","finaAmt":250000,"instlAmt":3290,"instlCnt":120,"maturityDate":null,"intrsRx":0.048,"rpsn":null,"camCode":null,"minAvlblAmt":5000,"minInstlAmt":400,"minInstlCnt":12,"maxAvlblAmt":280000,"maxInstlAmt":12081,"maxInstlCnt":120,"upsellAmt":0,"maxUpsellAmt":30000,"ccyId":"CZK","totRpmtAmt":0,"refiSavedAmt":0,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":true,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":250000,"minIrForFinalization":0.048,"origScOfrIntrsRx":null,"payCpcyForFinal":12081,"maxAvlblTopup":30000,"prodIntrsCodeId":null},{"loanPurpId":null,"applVariantKey":*************,"applVariantTpId":"REQ","applVariantSignCnlFlag":null,"busProdTpId":"RCL","busProdSubTpId":"RCL_REFI","finaAmt":300000,"instlAmt":3153,"instlCnt":120,"maturityDate":"2034-09-09","intrsRx":0.048,"rpsn":0.0491,"camCode":null,"minAvlblAmt":null,"minInstlAmt":null,"minInstlCnt":null,"maxAvlblAmt":null,"maxInstlAmt":null,"maxInstlCnt":null,"upsellAmt":0,"maxUpsellAmt":null,"ccyId":"CZK","totRpmtAmt":378360,"refiSavedAmt":5847,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":9000,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"equaOblgtnAmt":null,"nonEquaOblgtnAmt":300000,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null}]
                """;
        String jsonCtObligations = """
                [{"instOblgtnKey":390287,"virtOblgtnId":null,"adbInstPtKey":905261,"ofFirstInstlDate":null,"ofFinInstnId":null,"ofContrNum":null,"ofTotLoanAmt":null,"scTotLoanAmt":170000,"ofTotInstlCnt":null,"ofIntrsRx":null,"scIntrsRx":9.4,"ofInstl":null,"scInstl":2345,"ofOblgtnProdTpId":null,"scOblgtnProdTpId":"CRL","ofMaturityDate":null,"ofAmt":null,"scAmt":147000,"finInstnGrpCode":"1","equaContrNum":null,"ofrIntrsRx":0.089,"origScOfrIntrsRx":0.089,"origContractRequired":true,"scPrimaryOblgtn":true,"ofAccNum":null,"ofAccNumPrefix":null,"ccyId":"CZK","ofCrntAccFeeAmt":null,"ofContrFeeAmt":null,"ofPrinc":null,"ofInstlCnt":null,"ofOblgtnLoanPurpId":null,"ofBankId":null,"ofVarSymbol":null,"ofConstSymbol":null,"ofSpecSymbol":null,"ofOblgtnDocTpId":null,"ofComment":null,"scCcbContractId":"00065537K","clFinInstnId":"CS","scFinInstnCode":null,"clOrder":1,"scOrder":1,"oblgtnSelected":true,"scIsApplicable":true,"scIsMandatory":false,"scIntContrNum":null,"scIntSourceSystem":null,"scIntSourceSystemId":null,"primaryOblgtn":true,"finRepayedAmt":null,"totRepaymentFlag":null,"oblgtnAmtAfterRepayment":null,"delFlag":null,"scgRcmdFlag":false}]
                """;
        return new GetLoanApplicationResult(
                ApplicationUtils.convertJsonToType(jsonCtGetLoanAppl, CtGetLoanAppl.class),
                ApplicationUtils.convertJsonToType(jsonCtPrimaryOwner, CtPersonGet.class),
                null,
                null,
                ApplicationUtils.convertJsonToType(jsonCtApplVariants, new TypeReference<>() {
                }),
                ApplicationUtils.convertJsonToType(jsonCtObligations, new TypeReference<>() {
                }),
                null
        );
    }
}